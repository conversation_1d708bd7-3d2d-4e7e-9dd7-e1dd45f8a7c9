{"sample_id": "simple_codenet_174249", "dataset_name": "simple_codenet", "problem_id": "p34850", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p34850", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_174249_func_solve"], "timestamp": 1754139282.9066207}