{"sample_id": "simple_codenet_194199", "dataset_name": "simple_codenet", "problem_id": "p38840", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p38840", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_194199_func_solve"], "timestamp": 1754139318.4544384}