{"sample_id": "simple_codenet_075879", "dataset_name": "simple_codenet", "problem_id": "p15176", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p15176", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_075879_func_solve"], "timestamp": 1754139066.2164004}