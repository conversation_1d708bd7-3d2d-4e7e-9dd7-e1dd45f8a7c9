{"sample_id": "simple_codenet_174192", "dataset_name": "simple_codenet", "problem_id": "p34839", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p34839", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_174192_func_solve"], "timestamp": 1754139282.818322}