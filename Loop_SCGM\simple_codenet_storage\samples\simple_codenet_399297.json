{"sample_id": "simple_codenet_399297", "dataset_name": "simple_codenet", "problem_id": "p79860", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p79860", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_399297_func_solve"], "timestamp": 1754139677.9156747}