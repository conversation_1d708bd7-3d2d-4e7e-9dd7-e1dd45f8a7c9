{"sample_id": "simple_codenet_053598", "dataset_name": "simple_codenet", "problem_id": "p10720", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p10720", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_053598_func_solve"], "timestamp": 1754139019.2883017}