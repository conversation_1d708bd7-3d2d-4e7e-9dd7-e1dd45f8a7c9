{"sample_id": "simple_codenet_555996", "dataset_name": "massive_codenet", "problem_id": "p111200", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p111200", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_555996_func_solve"], "timestamp": 1754148018.509707}