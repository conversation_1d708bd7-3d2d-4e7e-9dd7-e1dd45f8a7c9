{"sample_id": "simple_codenet_327363", "dataset_name": "simple_codenet", "problem_id": "p65473", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p65473", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_327363_func_solve"], "timestamp": 1754139536.25508}