{"sample_id": "simple_codenet_556584", "dataset_name": "massive_codenet", "problem_id": "p111317", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p111317", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_556584_func_solve"], "timestamp": 1754148030.6893086}