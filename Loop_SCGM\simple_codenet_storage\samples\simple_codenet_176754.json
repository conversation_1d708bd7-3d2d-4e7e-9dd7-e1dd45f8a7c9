{"sample_id": "simple_codenet_176754", "dataset_name": "simple_codenet", "problem_id": "p35351", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35351", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_176754_func_solve"], "timestamp": 1754139286.8621}