{"sample_id": "simple_codenet_555888", "dataset_name": "massive_codenet", "problem_id": "p111178", "code": "function solve(n) {\n    return Array.from({length: n}, (_, i) => i).reduce((a, b) => a + b, 0);\n}", "language": "javascript", "problem_description": "CodeNet problem p111178", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "javascript"], "patterns": [], "timestamp": 1754148017.1097975}