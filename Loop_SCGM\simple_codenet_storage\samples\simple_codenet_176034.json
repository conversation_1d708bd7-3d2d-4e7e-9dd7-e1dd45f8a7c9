{"sample_id": "simple_codenet_176034", "dataset_name": "simple_codenet", "problem_id": "p35207", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35207", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_176034_func_solve"], "timestamp": 1754139285.6639585}