{"sample_id": "simple_codenet_176187", "dataset_name": "simple_codenet", "problem_id": "p35238", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35238", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_176187_func_solve"], "timestamp": 1754139285.9126008}