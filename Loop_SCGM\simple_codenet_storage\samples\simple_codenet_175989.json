{"sample_id": "simple_codenet_175989", "dataset_name": "simple_codenet", "problem_id": "p35198", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35198", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_175989_func_solve"], "timestamp": 1754139285.5874019}