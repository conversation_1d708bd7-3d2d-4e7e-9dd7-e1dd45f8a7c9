{"sample_id": "simple_codenet_555987", "dataset_name": "simple_codenet", "problem_id": "p111198", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p111198", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_555987_func_solve"], "timestamp": 1754140011.3742259}