{"sample_id": "simple_codenet_074574", "dataset_name": "simple_codenet", "problem_id": "p14915", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14915", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074574_func_solve"], "timestamp": 1754139063.438064}