{"sample_id": "simple_codenet_074853", "dataset_name": "simple_codenet", "problem_id": "p14971", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14971", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074853_func_solve"], "timestamp": 1754139063.9489965}