{"sample_id": "simple_codenet_135723", "dataset_name": "simple_codenet", "problem_id": "p27145", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27145", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135723_func_solve"], "timestamp": 1754139203.274357}