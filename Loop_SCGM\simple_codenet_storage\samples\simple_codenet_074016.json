{"sample_id": "simple_codenet_074016", "dataset_name": "simple_codenet", "problem_id": "p14804", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14804", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074016_func_solve"], "timestamp": 1754139062.3104353}