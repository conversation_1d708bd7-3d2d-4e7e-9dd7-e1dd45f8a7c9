{"sample_id": "simple_codenet_176271", "dataset_name": "simple_codenet", "problem_id": "p35255", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35255", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_176271_func_solve"], "timestamp": 1754139286.0768595}