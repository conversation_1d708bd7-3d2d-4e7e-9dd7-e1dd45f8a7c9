{"sample_id": "simple_codenet_074529", "dataset_name": "simple_codenet", "problem_id": "p14906", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14906", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074529_func_solve"], "timestamp": 1754139063.362657}