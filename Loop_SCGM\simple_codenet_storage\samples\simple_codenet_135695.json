{"sample_id": "simple_codenet_135695", "dataset_name": "simple_codenet", "problem_id": "p27140", "code": "import java.util.Arrays;\npublic class ArraySort {\n    public static void main(String[] args) {\n        int[] arr = {3, 1, 4, 1, 5};\n        Arrays.sort(arr);\n    }\n}", "language": "java", "problem_description": "CodeNet problem p27140", "test_cases": "", "libraries": ["<PERSON><PERSON><PERSON>"], "difficulty": "Hard", "tags": ["codenet", "java"], "patterns": ["simple_codenet_135695_method_main", "simple_codenet_135695_class_ArraySort"], "timestamp": 1754139203.2156208}