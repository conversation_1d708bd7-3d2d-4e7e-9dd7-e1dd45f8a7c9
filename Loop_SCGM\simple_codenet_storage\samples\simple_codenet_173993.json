{"sample_id": "simple_codenet_173993", "dataset_name": "simple_codenet", "problem_id": "p34799", "code": "import java.util.Arrays;\npublic class ArraySort {\n    public static void main(String[] args) {\n        int[] arr = {3, 1, 4, 1, 5};\n        Arrays.sort(arr);\n    }\n}", "language": "java", "problem_description": "CodeNet problem p34799", "test_cases": "", "libraries": ["<PERSON><PERSON><PERSON>"], "difficulty": "Hard", "tags": ["codenet", "java"], "patterns": ["simple_codenet_173993_method_main", "simple_codenet_173993_class_ArraySort"], "timestamp": 1754139282.4862158}