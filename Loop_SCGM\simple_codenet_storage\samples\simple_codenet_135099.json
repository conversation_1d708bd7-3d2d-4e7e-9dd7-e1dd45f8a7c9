{"sample_id": "simple_codenet_135099", "dataset_name": "simple_codenet", "problem_id": "p27020", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27020", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135099_func_solve"], "timestamp": 1754139201.7983675}