{"sample_id": "simple_codenet_097914", "dataset_name": "simple_codenet", "problem_id": "p19583", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p19583", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_097914_func_solve"], "timestamp": 1754139116.2122996}