{"sample_id": "simple_codenet_206025", "dataset_name": "simple_codenet", "problem_id": "p41206", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p41206", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_206025_func_solve"], "timestamp": 1754139339.2100086}