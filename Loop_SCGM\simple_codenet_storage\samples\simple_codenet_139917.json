{"sample_id": "simple_codenet_139917", "dataset_name": "simple_codenet", "problem_id": "p27984", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27984", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_139917_func_solve"], "timestamp": 1754139212.6889052}