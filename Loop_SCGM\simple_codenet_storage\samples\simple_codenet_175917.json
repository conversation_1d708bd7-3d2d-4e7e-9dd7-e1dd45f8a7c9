{"sample_id": "simple_codenet_175917", "dataset_name": "simple_codenet", "problem_id": "p35184", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35184", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_175917_func_solve"], "timestamp": 1754139285.4681168}