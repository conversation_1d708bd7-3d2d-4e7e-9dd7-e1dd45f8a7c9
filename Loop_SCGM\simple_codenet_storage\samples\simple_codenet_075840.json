{"sample_id": "simple_codenet_075840", "dataset_name": "simple_codenet", "problem_id": "p15169", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p15169", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_075840_func_solve"], "timestamp": 1754139066.1619709}