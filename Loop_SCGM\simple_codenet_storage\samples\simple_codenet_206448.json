{"sample_id": "simple_codenet_206448", "dataset_name": "massive_codenet", "problem_id": "p41290", "code": "function solve(n) {\n    return Array.from({length: n}, (_, i) => i).reduce((a, b) => a + b, 0);\n}", "language": "javascript", "problem_description": "CodeNet problem p41290", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "javascript"], "patterns": [], "timestamp": 1754148026.9852045}