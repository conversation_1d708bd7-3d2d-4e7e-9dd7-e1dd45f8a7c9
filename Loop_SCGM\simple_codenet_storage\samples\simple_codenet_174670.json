{"sample_id": "simple_codenet_174670", "dataset_name": "simple_codenet", "problem_id": "p34935", "code": "#include <iostream>\nusing namespace std;\nint main() {\n    int n; cin >> n;\n    cout << n * (n + 1) / 2 << endl;\n    return 0;\n}", "language": "c++", "problem_description": "CodeNet problem p34935", "test_cases": "", "libraries": ["iostream"], "difficulty": "Medium", "tags": ["codenet", "c++"], "patterns": ["simple_codenet_174670_func_main"], "timestamp": 1754139283.5481863}