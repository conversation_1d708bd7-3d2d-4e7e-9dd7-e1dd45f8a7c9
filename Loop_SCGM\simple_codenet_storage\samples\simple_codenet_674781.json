{"sample_id": "simple_codenet_674781", "dataset_name": "simple_codenet", "problem_id": "p134957", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p134957", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_674781_func_solve"], "timestamp": 1754140221.6265793}