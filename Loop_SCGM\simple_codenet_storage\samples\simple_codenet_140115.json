{"sample_id": "simple_codenet_140115", "dataset_name": "simple_codenet", "problem_id": "p28024", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p28024", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_140115_func_solve"], "timestamp": 1754139213.1643393}