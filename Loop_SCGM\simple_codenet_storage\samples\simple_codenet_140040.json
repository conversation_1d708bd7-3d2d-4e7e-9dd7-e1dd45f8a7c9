{"sample_id": "simple_codenet_140040", "dataset_name": "simple_codenet", "problem_id": "p28009", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p28009", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_140040_func_solve"], "timestamp": 1754139212.9824724}