#!/usr/bin/env python3
"""
🔍 SCGM Failure Analysis
Analyze why 1,571 issues failed to understand the gap from 31.5% to 65.4%
"""

import json
import csv
from collections import Counter, defaultdict

def analyze_failures():
    print("🔍 SCGM FAILURE ANALYSIS")
    print("=" * 60)
    print("🎯 Goal: Understand why 1,571 issues failed (31.5% vs 65.4% target)")
    print("=" * 60)
    
    # Load the comprehensive results
    with open('scgm_full_swe_bench_evaluation_full_20250804_181358.json', 'r') as f:
        data = json.load(f)
    
    # Load CSV for easier analysis
    csv_data = []
    with open('scgm_swe_bench_results_full_20250804_181401.csv', 'r') as f:
        reader = csv.DictReader(f)
        csv_data = list(reader)
    
    # Separate successes and failures
    failed_results = [r for r in data['detailed_results'] if not r.get('fix_success', False)]
    success_results = [r for r in data['detailed_results'] if r.get('fix_success', False)]
    
    print(f"📊 BASIC STATISTICS:")
    print(f"   Total Issues: {len(data['detailed_results'])}")
    print(f"   Successful: {len(success_results)} (31.5%)")
    print(f"   Failed: {len(failed_results)} (68.5%)")
    print(f"   Target Gap: 33.9% (need to fix ~778 more issues)")
    
    # Analyze failure reasons
    print(f"\n🔍 FAILURE REASON ANALYSIS:")
    failure_reasons = Counter()
    for result in failed_results:
        reason = result.get('failure_reason', 'Unknown')
        failure_reasons[reason] += 1
    
    for reason, count in failure_reasons.most_common(10):
        percentage = (count / len(failed_results)) * 100
        print(f"   {reason}: {count} ({percentage:.1f}%)")
    
    # Analyze pattern usage in failures vs successes
    print(f"\n🧠 PATTERN USAGE ANALYSIS:")
    
    # Failures by pattern count
    failed_pattern_counts = Counter()
    for result in failed_results:
        pattern_count = result.get('patterns_used', 0)
        failed_pattern_counts[pattern_count] += 1
    
    success_pattern_counts = Counter()
    for result in success_results:
        pattern_count = result.get('patterns_used', 0)
        success_pattern_counts[pattern_count] += 1
    
    print(f"   Pattern Usage in Failures:")
    for count, freq in sorted(failed_pattern_counts.items()):
        percentage = (freq / len(failed_results)) * 100
        print(f"     {count} patterns: {freq} issues ({percentage:.1f}%)")
    
    print(f"   Pattern Usage in Successes:")
    for count, freq in sorted(success_pattern_counts.items()):
        percentage = (freq / len(success_results)) * 100
        print(f"     {count} patterns: {freq} issues ({percentage:.1f}%)")
    
    # Analyze fix types
    print(f"\n🔧 FIX TYPE ANALYSIS:")
    
    failed_fix_types = Counter()
    for result in failed_results:
        fix_type = result.get('fix_type', 'unknown')
        failed_fix_types[fix_type] += 1
    
    success_fix_types = Counter()
    for result in success_results:
        fix_type = result.get('fix_type', 'unknown')
        success_fix_types[fix_type] += 1
    
    print(f"   Failed Fix Types:")
    for fix_type, count in failed_fix_types.most_common():
        percentage = (count / len(failed_results)) * 100
        print(f"     {fix_type}: {count} ({percentage:.1f}%)")
    
    print(f"   Successful Fix Types:")
    for fix_type, count in success_fix_types.most_common():
        percentage = (count / len(success_results)) * 100
        print(f"     {fix_type}: {count} ({percentage:.1f}%)")
    
    # Analyze processing times
    print(f"\n⏱️ PROCESSING TIME ANALYSIS:")
    
    failed_times = [r.get('processing_time', 0) for r in failed_results]
    success_times = [r.get('processing_time', 0) for r in success_results]
    
    print(f"   Failed Issues:")
    print(f"     Average: {sum(failed_times)/len(failed_times):.2f}s")
    print(f"     Min: {min(failed_times):.2f}s")
    print(f"     Max: {max(failed_times):.2f}s")
    
    print(f"   Successful Issues:")
    print(f"     Average: {sum(success_times)/len(success_times):.2f}s")
    print(f"     Min: {min(success_times):.2f}s")
    print(f"     Max: {max(success_times):.2f}s")
    
    return failed_results, success_results

if __name__ == "__main__":
    analyze_failures()
