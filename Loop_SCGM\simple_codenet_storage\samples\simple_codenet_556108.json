{"sample_id": "simple_codenet_556108", "dataset_name": "massive_codenet", "problem_id": "p111222", "code": "def fibon<PERSON>ci(n):\n    if n <= 1: return n\n    return fi<PERSON><PERSON><PERSON>(n-1) + <PERSON><PERSON><PERSON><PERSON>(n-2)", "language": "python", "problem_description": "CodeNet problem p111222", "test_cases": "", "libraries": [], "difficulty": "Medium", "tags": ["codenet", "python"], "patterns": ["simple_codenet_556108_func_<PERSON><PERSON><PERSON><PERSON>"], "timestamp": 1754148020.413902}