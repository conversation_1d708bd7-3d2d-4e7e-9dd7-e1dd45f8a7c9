{"sample_id": "simple_codenet_053025", "dataset_name": "simple_codenet", "problem_id": "p10606", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p10606", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_053025_func_solve"], "timestamp": 1754139018.3788238}