{"sample_id": "simple_codenet_155904", "dataset_name": "massive_codenet", "problem_id": "p31181", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p31181", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_155904_func_solve"], "timestamp": 1754148015.8208485}