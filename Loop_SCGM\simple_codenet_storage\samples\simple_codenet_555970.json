{"sample_id": "simple_codenet_555970", "dataset_name": "massive_codenet", "problem_id": "p111195", "code": "import java.util.*;\npublic class Solution {\n    public static void main(String[] args) {\n        Scanner sc = new Scanner(System.in);\n        int n = sc.nextInt();\n        System.out.println(n * (n + 1) / 2);\n    }\n}", "language": "java", "problem_description": "CodeNet problem p111195", "test_cases": "", "libraries": ["util"], "difficulty": "Medium", "tags": ["codenet", "java"], "patterns": ["simple_codenet_555970_func_Scanner"], "timestamp": 1754148018.190196}