{"sample_id": "simple_codenet_206724", "dataset_name": "massive_codenet", "problem_id": "p41345", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p41345", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_206724_func_solve"], "timestamp": 1754148031.1718884}