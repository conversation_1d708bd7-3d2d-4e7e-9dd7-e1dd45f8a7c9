{"sample_id": "simple_codenet_423109", "dataset_name": "simple_codenet", "problem_id": "p84622", "code": "#include <vector>\n#include <algorithm>\nusing namespace std;\nint main() {\n    vector<int> v = {3, 1, 4, 1, 5};\n    sort(v.begin(), v.end());\n    return 0;\n}", "language": "c++", "problem_description": "CodeNet problem p84622", "test_cases": "", "libraries": ["algorithm", "vector"], "difficulty": "Medium", "tags": ["codenet", "c++"], "patterns": [], "timestamp": 1754139743.7530694}