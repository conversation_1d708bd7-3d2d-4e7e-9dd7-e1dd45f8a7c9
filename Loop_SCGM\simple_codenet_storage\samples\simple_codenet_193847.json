{"sample_id": "simple_codenet_193847", "dataset_name": "simple_codenet", "problem_id": "p38770", "code": "import java.util.Arrays;\npublic class ArraySort {\n    public static void main(String[] args) {\n        int[] arr = {3, 1, 4, 1, 5};\n        Arrays.sort(arr);\n    }\n}", "language": "java", "problem_description": "CodeNet problem p38770", "test_cases": "", "libraries": ["<PERSON><PERSON><PERSON>"], "difficulty": "Hard", "tags": ["codenet", "java"], "patterns": ["simple_codenet_193847_method_main", "simple_codenet_193847_class_ArraySort"], "timestamp": 1754139317.9079676}