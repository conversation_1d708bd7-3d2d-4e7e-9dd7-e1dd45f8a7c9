{"sample_id": "simple_codenet_140037", "dataset_name": "simple_codenet", "problem_id": "p28008", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p28008", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_140037_func_solve"], "timestamp": 1754139212.9785318}