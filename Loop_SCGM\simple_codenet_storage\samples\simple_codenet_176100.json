{"sample_id": "simple_codenet_176100", "dataset_name": "simple_codenet", "problem_id": "p35221", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35221", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_176100_func_solve"], "timestamp": 1754139285.783465}