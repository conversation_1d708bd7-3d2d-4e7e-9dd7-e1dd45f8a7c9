{"sample_id": "simple_codenet_176223", "dataset_name": "simple_codenet", "problem_id": "p35245", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35245", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_176223_func_solve"], "timestamp": 1754139285.997935}