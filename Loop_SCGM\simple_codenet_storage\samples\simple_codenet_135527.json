{"sample_id": "simple_codenet_135527", "dataset_name": "simple_codenet", "problem_id": "p27106", "code": "import java.util.Arrays;\npublic class ArraySort {\n    public static void main(String[] args) {\n        int[] arr = {3, 1, 4, 1, 5};\n        Arrays.sort(arr);\n    }\n}", "language": "java", "problem_description": "CodeNet problem p27106", "test_cases": "", "libraries": ["<PERSON><PERSON><PERSON>"], "difficulty": "Hard", "tags": ["codenet", "java"], "patterns": ["simple_codenet_135527_method_main", "simple_codenet_135527_class_ArraySort"], "timestamp": 1754139202.839158}