{"sample_id": "simple_codenet_674607", "dataset_name": "simple_codenet", "problem_id": "p134922", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p134922", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_674607_func_solve"], "timestamp": 1754140221.2917454}