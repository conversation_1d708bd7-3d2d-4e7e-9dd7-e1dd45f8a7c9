{"sample_id": "simple_codenet_399141", "dataset_name": "simple_codenet", "problem_id": "p79829", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p79829", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_399141_func_solve"], "timestamp": 1754139677.612022}