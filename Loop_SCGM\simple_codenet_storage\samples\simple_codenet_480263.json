{"sample_id": "simple_codenet_480263", "dataset_name": "simple_codenet", "problem_id": "p96053", "code": "import java.util.Arrays;\npublic class ArraySort {\n    public static void main(String[] args) {\n        int[] arr = {3, 1, 4, 1, 5};\n        Arrays.sort(arr);\n    }\n}", "language": "java", "problem_description": "CodeNet problem p96053", "test_cases": "", "libraries": ["<PERSON><PERSON><PERSON>"], "difficulty": "Hard", "tags": ["codenet", "java"], "patterns": [], "timestamp": 1754139869.8760417}