{"sample_id": "simple_codenet_639600", "dataset_name": "simple_codenet", "problem_id": "p127921", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p127921", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_639600_func_solve"], "timestamp": 1754140156.9540818}