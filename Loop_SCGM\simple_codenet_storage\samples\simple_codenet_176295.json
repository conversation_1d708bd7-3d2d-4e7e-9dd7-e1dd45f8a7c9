{"sample_id": "simple_codenet_176295", "dataset_name": "simple_codenet", "problem_id": "p35260", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35260", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_176295_func_solve"], "timestamp": 1754139286.1129591}