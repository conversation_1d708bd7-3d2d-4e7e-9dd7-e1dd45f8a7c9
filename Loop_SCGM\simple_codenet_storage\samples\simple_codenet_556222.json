{"sample_id": "simple_codenet_556222", "dataset_name": "massive_codenet", "problem_id": "p111245", "code": "import java.util.*;\npublic class Solution {\n    public static void main(String[] args) {\n        Scanner sc = new Scanner(System.in);\n        int n = sc.nextInt();\n        System.out.println(n * (n + 1) / 2);\n    }\n}", "language": "java", "problem_description": "CodeNet problem p111245", "test_cases": "", "libraries": ["util"], "difficulty": "Medium", "tags": ["codenet", "java"], "patterns": ["simple_codenet_556222_func_Scanner"], "timestamp": 1754148024.7330985}