{"sample_id": "simple_codenet_174492", "dataset_name": "simple_codenet", "problem_id": "p34899", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p34899", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_174492_func_solve"], "timestamp": 1754139283.2974186}