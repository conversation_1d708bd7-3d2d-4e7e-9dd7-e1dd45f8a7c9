{"sample_id": "simple_codenet_135825", "dataset_name": "simple_codenet", "problem_id": "p27166", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27166", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135825_func_solve"], "timestamp": 1754139203.4688473}