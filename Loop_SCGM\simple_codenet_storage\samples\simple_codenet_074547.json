{"sample_id": "simple_codenet_074547", "dataset_name": "simple_codenet", "problem_id": "p14910", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14910", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074547_func_solve"], "timestamp": 1754139063.393142}