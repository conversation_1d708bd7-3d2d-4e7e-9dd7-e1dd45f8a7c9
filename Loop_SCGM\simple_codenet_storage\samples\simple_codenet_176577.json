{"sample_id": "simple_codenet_176577", "dataset_name": "simple_codenet", "problem_id": "p35316", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35316", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_176577_func_solve"], "timestamp": 1754139286.6086361}