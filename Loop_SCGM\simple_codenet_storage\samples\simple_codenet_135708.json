{"sample_id": "simple_codenet_135708", "dataset_name": "simple_codenet", "problem_id": "p27142", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27142", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135708_func_solve"], "timestamp": 1754139203.2400548}