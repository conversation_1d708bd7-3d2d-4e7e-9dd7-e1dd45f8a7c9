{"sample_id": "simple_codenet_098119", "dataset_name": "simple_codenet", "problem_id": "p19624", "code": "#include <vector>\n#include <algorithm>\nusing namespace std;\nint main() {\n    vector<int> v = {3, 1, 4, 1, 5};\n    sort(v.begin(), v.end());\n    return 0;\n}", "language": "c++", "problem_description": "CodeNet problem p19624", "test_cases": "", "libraries": ["algorithm", "vector"], "difficulty": "Medium", "tags": ["codenet", "c++"], "patterns": [], "timestamp": 1754139116.6293962}