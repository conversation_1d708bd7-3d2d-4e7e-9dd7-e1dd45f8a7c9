{"sample_id": "simple_codenet_556113", "dataset_name": "simple_codenet", "problem_id": "p111223", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p111223", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_556113_func_solve"], "timestamp": 1754140011.5722308}