{"sample_id": "simple_codenet_074241", "dataset_name": "simple_codenet", "problem_id": "p14849", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14849", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074241_func_solve"], "timestamp": 1754139062.7379212}