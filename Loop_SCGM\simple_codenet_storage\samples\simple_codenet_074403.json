{"sample_id": "simple_codenet_074403", "dataset_name": "simple_codenet", "problem_id": "p14881", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14881", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074403_func_solve"], "timestamp": 1754139063.1333814}