{"sample_id": "simple_codenet_423849", "dataset_name": "simple_codenet", "problem_id": "p84770", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p84770", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_423849_func_solve"], "timestamp": 1754139745.7429092}