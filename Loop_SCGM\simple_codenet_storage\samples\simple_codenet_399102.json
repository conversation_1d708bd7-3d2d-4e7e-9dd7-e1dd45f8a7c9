{"sample_id": "simple_codenet_399102", "dataset_name": "simple_codenet", "problem_id": "p79821", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p79821", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_399102_func_solve"], "timestamp": 1754139677.5337956}