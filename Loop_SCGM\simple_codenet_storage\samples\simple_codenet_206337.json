{"sample_id": "simple_codenet_206337", "dataset_name": "massive_codenet", "problem_id": "p41268", "code": "package main\nimport (\n    \"fmt\"\n    \"sort\"\n)\nfunc main() {\n    arr := []int{3, 1, 4, 1, 5}\n    sort.Ints(arr)\n    fmt.Println(arr)\n}", "language": "go", "problem_description": "CodeNet problem p41268", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "go"], "patterns": [], "timestamp": 1754148025.2888544}