{"sample_id": "simple_codenet_174795", "dataset_name": "simple_codenet", "problem_id": "p34960", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p34960", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_174795_func_solve"], "timestamp": 1754139283.719705}