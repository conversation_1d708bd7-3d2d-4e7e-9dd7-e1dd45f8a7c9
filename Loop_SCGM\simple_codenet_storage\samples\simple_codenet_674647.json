{"sample_id": "simple_codenet_674647", "dataset_name": "simple_codenet", "problem_id": "p134930", "code": "#include <vector>\n#include <algorithm>\nusing namespace std;\nint main() {\n    vector<int> v = {3, 1, 4, 1, 5};\n    sort(v.begin(), v.end());\n    return 0;\n}", "language": "c++", "problem_description": "CodeNet problem p134930", "test_cases": "", "libraries": ["algorithm", "vector"], "difficulty": "Medium", "tags": ["codenet", "c++"], "patterns": [], "timestamp": 1754140221.3668659}