{"sample_id": "simple_codenet_555940", "dataset_name": "massive_codenet", "problem_id": "p111189", "code": "def fibon<PERSON>ci(n):\n    if n <= 1: return n\n    return fi<PERSON><PERSON><PERSON>(n-1) + <PERSON><PERSON><PERSON><PERSON>(n-2)", "language": "python", "problem_description": "CodeNet problem p111189", "test_cases": "", "libraries": [], "difficulty": "Medium", "tags": ["codenet", "python"], "patterns": ["simple_codenet_555940_func_<PERSON><PERSON><PERSON><PERSON>"], "timestamp": 1754148017.7548625}