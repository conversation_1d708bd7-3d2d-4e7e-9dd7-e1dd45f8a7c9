{"sample_id": "simple_codenet_098145", "dataset_name": "simple_codenet", "problem_id": "p19630", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p19630", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_098145_func_solve"], "timestamp": 1754139116.6869836}