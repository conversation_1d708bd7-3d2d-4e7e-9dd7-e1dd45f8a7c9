{"sample_id": "simple_codenet_073932", "dataset_name": "simple_codenet", "problem_id": "p14787", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14787", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_073932_func_solve"], "timestamp": 1754139062.1344497}