{"sample_id": "simple_codenet_194220", "dataset_name": "simple_codenet", "problem_id": "p38845", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p38845", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_194220_func_solve"], "timestamp": 1754139318.4953518}