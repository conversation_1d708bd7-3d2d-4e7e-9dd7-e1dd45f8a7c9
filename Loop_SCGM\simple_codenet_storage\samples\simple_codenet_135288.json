{"sample_id": "simple_codenet_135288", "dataset_name": "simple_codenet", "problem_id": "p27058", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27058", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135288_func_solve"], "timestamp": 1754139202.245937}