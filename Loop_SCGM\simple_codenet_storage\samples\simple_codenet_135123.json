{"sample_id": "simple_codenet_135123", "dataset_name": "simple_codenet", "problem_id": "p27025", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27025", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135123_func_solve"], "timestamp": 1754139201.8544183}