{"sample_id": "simple_codenet_399180", "dataset_name": "simple_codenet", "problem_id": "p79837", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p79837", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_399180_func_solve"], "timestamp": 1754139677.6840632}