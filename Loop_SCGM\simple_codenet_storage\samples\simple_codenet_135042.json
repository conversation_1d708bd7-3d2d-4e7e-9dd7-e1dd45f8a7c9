{"sample_id": "simple_codenet_135042", "dataset_name": "simple_codenet", "problem_id": "p27009", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27009", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135042_func_solve"], "timestamp": 1754139201.6635575}