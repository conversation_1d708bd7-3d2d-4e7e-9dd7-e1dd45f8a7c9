{"sample_id": "simple_codenet_555744", "dataset_name": "massive_codenet", "problem_id": "p111149", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p111149", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_555744_func_solve"], "timestamp": 1754148015.2666316}