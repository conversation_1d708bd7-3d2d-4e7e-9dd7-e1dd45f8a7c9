{"sample_id": "simple_codenet_174753", "dataset_name": "simple_codenet", "problem_id": "p34951", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p34951", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_174753_func_solve"], "timestamp": 1754139283.680643}