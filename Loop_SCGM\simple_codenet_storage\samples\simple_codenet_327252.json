{"sample_id": "simple_codenet_327252", "dataset_name": "simple_codenet", "problem_id": "p65451", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p65451", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_327252_func_solve"], "timestamp": 1754139535.9567482}