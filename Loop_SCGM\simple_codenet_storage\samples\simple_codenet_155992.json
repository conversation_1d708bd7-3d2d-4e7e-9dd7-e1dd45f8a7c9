{"sample_id": "simple_codenet_155992", "dataset_name": "massive_codenet", "problem_id": "p31199", "code": "function solve(n) {\n    return Array.from({length: n}, (_, i) => i).reduce((a, b) => a + b, 0);\n}", "language": "javascript", "problem_description": "CodeNet problem p31199", "test_cases": "", "libraries": [], "difficulty": "Medium", "tags": ["codenet", "javascript"], "patterns": [], "timestamp": 1754148017.0028079}