{"sample_id": "simple_codenet_073989", "dataset_name": "simple_codenet", "problem_id": "p14798", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14798", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_073989_func_solve"], "timestamp": 1754139062.2490623}