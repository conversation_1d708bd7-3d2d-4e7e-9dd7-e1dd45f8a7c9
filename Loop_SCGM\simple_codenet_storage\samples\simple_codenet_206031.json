{"sample_id": "simple_codenet_206031", "dataset_name": "simple_codenet", "problem_id": "p41207", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p41207", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_206031_func_solve"], "timestamp": 1754139339.2128994}