{"sample_id": "simple_codenet_176142", "dataset_name": "simple_codenet", "problem_id": "p35229", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35229", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_176142_func_solve"], "timestamp": 1754139285.8446462}