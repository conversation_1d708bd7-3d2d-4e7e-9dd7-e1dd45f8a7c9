{"sample_id": "simple_codenet_176199", "dataset_name": "simple_codenet", "problem_id": "p35240", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35240", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_176199_func_solve"], "timestamp": 1754139285.9576046}