{"sample_id": "simple_codenet_135135", "dataset_name": "simple_codenet", "problem_id": "p27028", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27028", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135135_func_solve"], "timestamp": 1754139201.8833337}