{"sample_id": "simple_codenet_423894", "dataset_name": "simple_codenet", "problem_id": "p84779", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p84779", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_423894_func_solve"], "timestamp": 1754139745.8359044}