{"sample_id": "simple_codenet_555912", "dataset_name": "massive_codenet", "problem_id": "p111183", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p111183", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_555912_func_solve"], "timestamp": 1754148017.394146}