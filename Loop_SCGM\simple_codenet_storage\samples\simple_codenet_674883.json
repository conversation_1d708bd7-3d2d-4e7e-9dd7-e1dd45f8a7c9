{"sample_id": "simple_codenet_674883", "dataset_name": "simple_codenet", "problem_id": "p134977", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p134977", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_674883_func_solve"], "timestamp": 1754140221.8206685}