{"sample_id": "simple_codenet_174501", "dataset_name": "simple_codenet", "problem_id": "p34901", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p34901", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_174501_func_solve"], "timestamp": 1754139283.3113837}