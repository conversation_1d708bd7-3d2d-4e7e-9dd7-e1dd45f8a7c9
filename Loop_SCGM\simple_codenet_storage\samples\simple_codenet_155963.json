{"sample_id": "simple_codenet_155963", "dataset_name": "massive_codenet", "problem_id": "p31193", "code": "#include <stdio.h>\n#include <stdlib.h>\nint compare(const void *a, const void *b) {\n    return (*(int*)a - *(int*)b);\n}\nint main() {\n    int arr[] = {3, 1, 4, 1, 5};\n    qsort(arr, 5, sizeof(int), compare);\n    return 0;\n}", "language": "c", "problem_description": "CodeNet problem p31193", "test_cases": "", "libraries": ["stdlib", "stdio"], "difficulty": "Hard", "tags": ["codenet", "c"], "patterns": ["simple_codenet_155963_func_compare"], "timestamp": 1754148016.612611}