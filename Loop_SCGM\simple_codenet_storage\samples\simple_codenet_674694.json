{"sample_id": "simple_codenet_674694", "dataset_name": "simple_codenet", "problem_id": "p134939", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p134939", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_674694_func_solve"], "timestamp": 1754140221.463671}