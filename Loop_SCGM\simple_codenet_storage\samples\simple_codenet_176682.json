{"sample_id": "simple_codenet_176682", "dataset_name": "simple_codenet", "problem_id": "p35337", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35337", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_176682_func_solve"], "timestamp": 1754139286.7613285}