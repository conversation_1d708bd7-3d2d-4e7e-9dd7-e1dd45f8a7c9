{"sample_id": "simple_codenet_053484", "dataset_name": "massive_codenet", "problem_id": "p10697", "code": "function solve(n) {\n    return Array.from({length: n}, (_, i) => i).reduce((a, b) => a + b, 0);\n}", "language": "javascript", "problem_description": "CodeNet problem p10697", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "javascript"], "patterns": [], "timestamp": 1754147982.35663}