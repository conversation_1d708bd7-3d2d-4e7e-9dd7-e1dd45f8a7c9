{"sample_id": "simple_codenet_074907", "dataset_name": "simple_codenet", "problem_id": "p14982", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14982", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074907_func_solve"], "timestamp": 1754139064.0620534}