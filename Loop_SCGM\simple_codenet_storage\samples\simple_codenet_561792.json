{"sample_id": "simple_codenet_561792", "dataset_name": "massive_codenet", "problem_id": "p112359", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p112359", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_561792_func_solve"], "timestamp": 1754148109.504051}