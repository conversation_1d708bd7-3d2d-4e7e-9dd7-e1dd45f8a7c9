{"sample_id": "simple_codenet_173896", "dataset_name": "simple_codenet", "problem_id": "p34780", "code": "#include <iostream>\nusing namespace std;\nint main() {\n    int n; cin >> n;\n    cout << n * (n + 1) / 2 << endl;\n    return 0;\n}", "language": "c++", "problem_description": "CodeNet problem p34780", "test_cases": "", "libraries": ["iostream"], "difficulty": "Medium", "tags": ["codenet", "c++"], "patterns": ["simple_codenet_173896_func_main"], "timestamp": 1754139282.3431523}