{"sample_id": "simple_codenet_194058", "dataset_name": "simple_codenet", "problem_id": "p38812", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p38812", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_194058_func_solve"], "timestamp": 1754139318.2139282}