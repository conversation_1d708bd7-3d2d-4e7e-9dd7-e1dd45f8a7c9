{"sample_id": "simple_codenet_423552", "dataset_name": "simple_codenet", "problem_id": "p84711", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p84711", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_423552_func_solve"], "timestamp": 1754139744.9606988}