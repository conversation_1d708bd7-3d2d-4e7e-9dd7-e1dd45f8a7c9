{"sample_id": "simple_codenet_399279", "dataset_name": "simple_codenet", "problem_id": "p79856", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p79856", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_399279_func_solve"], "timestamp": 1754139677.8865933}