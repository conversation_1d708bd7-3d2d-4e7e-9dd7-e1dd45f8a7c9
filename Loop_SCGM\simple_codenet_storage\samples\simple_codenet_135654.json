{"sample_id": "simple_codenet_135654", "dataset_name": "simple_codenet", "problem_id": "p27131", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27131", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135654_func_solve"], "timestamp": 1754139203.1389983}