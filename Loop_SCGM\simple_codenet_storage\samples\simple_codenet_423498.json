{"sample_id": "simple_codenet_423498", "dataset_name": "simple_codenet", "problem_id": "p84700", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p84700", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_423498_func_solve"], "timestamp": 1754139744.8226087}