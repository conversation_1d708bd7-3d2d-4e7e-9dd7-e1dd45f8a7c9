{"sample_id": "simple_codenet_399345", "dataset_name": "simple_codenet", "problem_id": "p79870", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p79870", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_399345_func_solve"], "timestamp": 1754139678.0146313}