{"sample_id": "simple_codenet_173927", "dataset_name": "simple_codenet", "problem_id": "p34786", "code": "import java.util.Arrays;\npublic class ArraySort {\n    public static void main(String[] args) {\n        int[] arr = {3, 1, 4, 1, 5};\n        Arrays.sort(arr);\n    }\n}", "language": "java", "problem_description": "CodeNet problem p34786", "test_cases": "", "libraries": ["<PERSON><PERSON><PERSON>"], "difficulty": "Hard", "tags": ["codenet", "java"], "patterns": ["simple_codenet_173927_method_main", "simple_codenet_173927_class_ArraySort"], "timestamp": 1754139282.3718805}