{"sample_id": "simple_codenet_074274", "dataset_name": "simple_codenet", "problem_id": "p14855", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14855", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074274_func_solve"], "timestamp": 1754139062.8057394}