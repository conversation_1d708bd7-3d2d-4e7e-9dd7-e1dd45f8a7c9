{"sample_id": "simple_codenet_327456", "dataset_name": "simple_codenet", "problem_id": "p65492", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p65492", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_327456_func_solve"], "timestamp": 1754139536.4649405}