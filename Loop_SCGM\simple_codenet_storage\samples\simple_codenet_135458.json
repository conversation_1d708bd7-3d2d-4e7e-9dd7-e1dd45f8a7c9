{"sample_id": "simple_codenet_135458", "dataset_name": "simple_codenet", "problem_id": "p27092", "code": "import java.util.*;\npublic class Solution {\n    public static void main(String[] args) {\n        Scanner sc = new Scanner(System.in);\n        int n = sc.nextInt();\n        System.out.println(n * (n + 1) / 2);\n    }\n}", "language": "java", "problem_description": "CodeNet problem p27092", "test_cases": "", "libraries": ["util"], "difficulty": "Hard", "tags": ["codenet", "java"], "patterns": ["simple_codenet_135458_method_main", "simple_codenet_135458_method_Scanner", "simple_codenet_135458_class_Solution"], "timestamp": 1754139202.6712291}