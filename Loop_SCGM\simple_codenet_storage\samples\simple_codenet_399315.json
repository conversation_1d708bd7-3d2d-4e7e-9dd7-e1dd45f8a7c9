{"sample_id": "simple_codenet_399315", "dataset_name": "simple_codenet", "problem_id": "p79864", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p79864", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_399315_func_solve"], "timestamp": 1754139677.9474776}