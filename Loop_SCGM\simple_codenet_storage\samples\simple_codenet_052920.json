{"sample_id": "simple_codenet_052920", "dataset_name": "massive_codenet", "problem_id": "p10585", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p10585", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_052920_func_solve"], "timestamp": 1754147974.3336358}