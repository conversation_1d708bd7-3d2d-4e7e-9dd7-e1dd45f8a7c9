{"sample_id": "simple_codenet_206808", "dataset_name": "massive_codenet", "problem_id": "p41362", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p41362", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_206808_func_solve"], "timestamp": 1754148032.533081}