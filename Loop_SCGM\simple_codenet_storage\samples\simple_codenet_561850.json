{"sample_id": "simple_codenet_561850", "dataset_name": "massive_codenet", "problem_id": "p112371", "code": "import java.util.*;\npublic class Solution {\n    public static void main(String[] args) {\n        Scanner sc = new Scanner(System.in);\n        int n = sc.nextInt();\n        System.out.println(n * (n + 1) / 2);\n    }\n}", "language": "java", "problem_description": "CodeNet problem p112371", "test_cases": "", "libraries": ["util"], "difficulty": "Medium", "tags": ["codenet", "java"], "patterns": ["simple_codenet_561850_func_Scanner"], "timestamp": 1754148110.2933419}