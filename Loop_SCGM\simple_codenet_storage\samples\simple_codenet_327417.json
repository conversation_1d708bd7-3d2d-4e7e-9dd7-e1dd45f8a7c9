{"sample_id": "simple_codenet_327417", "dataset_name": "simple_codenet", "problem_id": "p65484", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p65484", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_327417_func_solve"], "timestamp": 1754139536.4062192}