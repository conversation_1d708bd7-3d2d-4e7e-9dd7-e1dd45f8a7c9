{"sample_id": "simple_codenet_053760", "dataset_name": "massive_codenet", "problem_id": "p10753", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p10753", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_053760_func_solve"], "timestamp": 1754147986.205214}