{"sample_id": "simple_codenet_674991", "dataset_name": "simple_codenet", "problem_id": "p134999", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p134999", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_674991_func_solve"], "timestamp": 1754140222.0805037}