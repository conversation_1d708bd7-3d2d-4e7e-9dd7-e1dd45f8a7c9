{"sample_id": "simple_codenet_639597", "dataset_name": "simple_codenet", "problem_id": "p127920", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p127920", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_639597_func_solve"], "timestamp": 1754140156.943538}