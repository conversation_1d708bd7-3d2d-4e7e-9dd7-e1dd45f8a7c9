{"sample_id": "simple_codenet_176394", "dataset_name": "simple_codenet", "problem_id": "p35279", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35279", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_176394_func_solve"], "timestamp": 1754139286.285654}