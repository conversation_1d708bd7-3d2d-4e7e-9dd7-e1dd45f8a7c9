{"sample_id": "simple_codenet_327372", "dataset_name": "simple_codenet", "problem_id": "p65475", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p65475", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_327372_func_solve"], "timestamp": 1754139536.278047}