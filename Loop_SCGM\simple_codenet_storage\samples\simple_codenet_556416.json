{"sample_id": "simple_codenet_556416", "dataset_name": "massive_codenet", "problem_id": "p111284", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p111284", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_556416_func_solve"], "timestamp": 1754148028.1072528}