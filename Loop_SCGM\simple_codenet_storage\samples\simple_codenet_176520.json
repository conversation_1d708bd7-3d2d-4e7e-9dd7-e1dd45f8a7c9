{"sample_id": "simple_codenet_176520", "dataset_name": "simple_codenet", "problem_id": "p35305", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35305", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_176520_func_solve"], "timestamp": 1754139286.5029695}