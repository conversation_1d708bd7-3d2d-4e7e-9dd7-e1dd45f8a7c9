{"sample_id": "simple_codenet_327321", "dataset_name": "simple_codenet", "problem_id": "p65465", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p65465", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_327321_func_solve"], "timestamp": 1754139536.1442268}