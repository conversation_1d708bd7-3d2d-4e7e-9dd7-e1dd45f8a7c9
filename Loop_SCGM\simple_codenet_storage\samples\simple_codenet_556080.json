{"sample_id": "simple_codenet_556080", "dataset_name": "massive_codenet", "problem_id": "p111217", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p111217", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_556080_func_solve"], "timestamp": 1754148019.6432254}