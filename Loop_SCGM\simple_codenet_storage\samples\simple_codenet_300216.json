{"sample_id": "simple_codenet_300216", "dataset_name": "massive_codenet", "problem_id": "p60044", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p60044", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_300216_func_solve"], "timestamp": 1754147936.6561675}