{"sample_id": "simple_codenet_639660", "dataset_name": "simple_codenet", "problem_id": "p127933", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p127933", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_639660_func_solve"], "timestamp": 1754140157.0856647}