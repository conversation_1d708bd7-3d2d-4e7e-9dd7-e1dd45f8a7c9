{"sample_id": "simple_codenet_327150", "dataset_name": "simple_codenet", "problem_id": "p65431", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p65431", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_327150_func_solve"], "timestamp": 1754139535.7449758}