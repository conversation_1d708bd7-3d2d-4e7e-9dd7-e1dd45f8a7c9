{"sample_id": "simple_codenet_555687", "dataset_name": "simple_codenet", "problem_id": "p111138", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p111138", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_555687_func_solve"], "timestamp": 1754140010.8298056}