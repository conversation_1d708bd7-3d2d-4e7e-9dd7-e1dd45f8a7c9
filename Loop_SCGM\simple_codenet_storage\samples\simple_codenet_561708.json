{"sample_id": "simple_codenet_561708", "dataset_name": "massive_codenet", "problem_id": "p112342", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p112342", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_561708_func_solve"], "timestamp": 1754148108.2933137}