{"sample_id": "simple_codenet_193958", "dataset_name": "simple_codenet", "problem_id": "p38792", "code": "import java.util.*;\npublic class Solution {\n    public static void main(String[] args) {\n        Scanner sc = new Scanner(System.in);\n        int n = sc.nextInt();\n        System.out.println(n * (n + 1) / 2);\n    }\n}", "language": "java", "problem_description": "CodeNet problem p38792", "test_cases": "", "libraries": ["util"], "difficulty": "Hard", "tags": ["codenet", "java"], "patterns": ["simple_codenet_193958_method_main", "simple_codenet_193958_method_Scanner", "simple_codenet_193958_class_Solution"], "timestamp": 1754139318.0621443}