{"sample_id": "simple_codenet_174840", "dataset_name": "simple_codenet", "problem_id": "p34969", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p34969", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_174840_func_solve"], "timestamp": 1754139283.8046365}