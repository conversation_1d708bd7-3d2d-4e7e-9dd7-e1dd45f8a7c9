{"sample_id": "simple_codenet_327441", "dataset_name": "simple_codenet", "problem_id": "p65489", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p65489", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_327441_func_solve"], "timestamp": 1754139536.4424224}