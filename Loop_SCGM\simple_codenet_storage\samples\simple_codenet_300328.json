{"sample_id": "simple_codenet_300328", "dataset_name": "massive_codenet", "problem_id": "p60066", "code": "def fibon<PERSON>ci(n):\n    if n <= 1: return n\n    return fi<PERSON><PERSON><PERSON>(n-1) + <PERSON><PERSON><PERSON><PERSON>(n-2)", "language": "python", "problem_description": "CodeNet problem p60066", "test_cases": "", "libraries": [], "difficulty": "Medium", "tags": ["codenet", "python"], "patterns": ["simple_codenet_300328_func_<PERSON><PERSON><PERSON><PERSON>"], "timestamp": 1754147938.2797232}