{"sample_id": "simple_codenet_074841", "dataset_name": "simple_codenet", "problem_id": "p14969", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14969", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074841_func_solve"], "timestamp": 1754139063.9229462}