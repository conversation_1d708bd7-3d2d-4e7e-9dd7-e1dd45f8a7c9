{"sample_id": "simple_codenet_140082", "dataset_name": "simple_codenet", "problem_id": "p28017", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p28017", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_140082_func_solve"], "timestamp": 1754139213.0949337}