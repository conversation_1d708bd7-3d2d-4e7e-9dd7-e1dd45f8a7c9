{"sample_id": "simple_codenet_176549", "dataset_name": "simple_codenet", "problem_id": "p35310", "code": "import java.util.Arrays;\npublic class ArraySort {\n    public static void main(String[] args) {\n        int[] arr = {3, 1, 4, 1, 5};\n        Arrays.sort(arr);\n    }\n}", "language": "java", "problem_description": "CodeNet problem p35310", "test_cases": "", "libraries": ["<PERSON><PERSON><PERSON>"], "difficulty": "Hard", "tags": ["codenet", "java"], "patterns": [], "timestamp": 1754139286.55335}