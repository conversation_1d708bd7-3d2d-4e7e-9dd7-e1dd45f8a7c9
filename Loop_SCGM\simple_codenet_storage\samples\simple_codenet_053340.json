{"sample_id": "simple_codenet_053340", "dataset_name": "massive_codenet", "problem_id": "p10669", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p10669", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_053340_func_solve"], "timestamp": 1754147980.1178544}