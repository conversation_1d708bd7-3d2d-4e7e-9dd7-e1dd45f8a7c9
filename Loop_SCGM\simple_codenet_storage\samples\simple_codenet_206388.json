{"sample_id": "simple_codenet_206388", "dataset_name": "massive_codenet", "problem_id": "p41278", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p41278", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_206388_func_solve"], "timestamp": 1754148025.9455729}