{"sample_id": "simple_codenet_674985", "dataset_name": "simple_codenet", "problem_id": "p134998", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p134998", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_674985_func_solve"], "timestamp": 1754140222.0677872}