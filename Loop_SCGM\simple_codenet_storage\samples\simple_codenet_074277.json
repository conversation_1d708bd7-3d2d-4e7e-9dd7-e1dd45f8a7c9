{"sample_id": "simple_codenet_074277", "dataset_name": "simple_codenet", "problem_id": "p14856", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14856", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074277_func_solve"], "timestamp": 1754139062.813022}