{"sample_id": "simple_codenet_135807", "dataset_name": "simple_codenet", "problem_id": "p27162", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27162", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135807_func_solve"], "timestamp": 1754139203.4311504}