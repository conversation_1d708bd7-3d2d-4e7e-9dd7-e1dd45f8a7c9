{"sample_id": "simple_codenet_327378", "dataset_name": "simple_codenet", "problem_id": "p65476", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p65476", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_327378_func_solve"], "timestamp": 1754139536.2945092}