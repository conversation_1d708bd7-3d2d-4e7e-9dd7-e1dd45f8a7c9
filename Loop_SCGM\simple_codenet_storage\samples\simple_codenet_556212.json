{"sample_id": "simple_codenet_556212", "dataset_name": "simple_codenet", "problem_id": "p111243", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p111243", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_556212_func_solve"], "timestamp": 1754140011.7199864}