{"sample_id": "simple_codenet_674871", "dataset_name": "simple_codenet", "problem_id": "p134975", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p134975", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_674871_func_solve"], "timestamp": 1754140221.800148}