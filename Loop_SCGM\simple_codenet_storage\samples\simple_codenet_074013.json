{"sample_id": "simple_codenet_074013", "dataset_name": "simple_codenet", "problem_id": "p14803", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14803", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074013_func_solve"], "timestamp": 1754139062.3037498}