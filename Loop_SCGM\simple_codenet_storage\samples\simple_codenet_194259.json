{"sample_id": "simple_codenet_194259", "dataset_name": "simple_codenet", "problem_id": "p38852", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p38852", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_194259_func_solve"], "timestamp": 1754139318.5675128}