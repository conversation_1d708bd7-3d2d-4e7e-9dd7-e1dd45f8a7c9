{"sample_id": "simple_codenet_674805", "dataset_name": "simple_codenet", "problem_id": "p134962", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p134962", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_674805_func_solve"], "timestamp": 1754140221.6709204}