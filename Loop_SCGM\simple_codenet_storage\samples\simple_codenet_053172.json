{"sample_id": "simple_codenet_053172", "dataset_name": "massive_codenet", "problem_id": "p10635", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p10635", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_053172_func_solve"], "timestamp": 1754147977.686308}