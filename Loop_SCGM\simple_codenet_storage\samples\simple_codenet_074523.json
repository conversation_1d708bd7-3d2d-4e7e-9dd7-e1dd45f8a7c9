{"sample_id": "simple_codenet_074523", "dataset_name": "simple_codenet", "problem_id": "p14905", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14905", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074523_func_solve"], "timestamp": 1754139063.3518643}