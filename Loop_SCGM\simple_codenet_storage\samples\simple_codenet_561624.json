{"sample_id": "simple_codenet_561624", "dataset_name": "massive_codenet", "problem_id": "p112325", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p112325", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_561624_func_solve"], "timestamp": 1754148107.0797532}