{"sample_id": "simple_codenet_423138", "dataset_name": "simple_codenet", "problem_id": "p84628", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p84628", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_423138_func_solve"], "timestamp": 1754139743.8792138}