{"sample_id": "simple_codenet_399174", "dataset_name": "simple_codenet", "problem_id": "p79835", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p79835", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_399174_func_solve"], "timestamp": 1754139677.6727428}