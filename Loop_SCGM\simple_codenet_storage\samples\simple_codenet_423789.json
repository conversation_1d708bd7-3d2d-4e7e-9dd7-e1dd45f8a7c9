{"sample_id": "simple_codenet_423789", "dataset_name": "simple_codenet", "problem_id": "p84758", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p84758", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_423789_func_solve"], "timestamp": 1754139745.6075497}