{"sample_id": "simple_codenet_135402", "dataset_name": "simple_codenet", "problem_id": "p27081", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27081", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135402_func_solve"], "timestamp": 1754139202.5022016}