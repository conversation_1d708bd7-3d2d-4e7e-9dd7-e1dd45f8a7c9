{"sample_id": "simple_codenet_423858", "dataset_name": "simple_codenet", "problem_id": "p84772", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p84772", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_423858_func_solve"], "timestamp": 1754139745.7608187}