{"sample_id": "simple_codenet_399216", "dataset_name": "simple_codenet", "problem_id": "p79844", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p79844", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_399216_func_solve"], "timestamp": 1754139677.76199}