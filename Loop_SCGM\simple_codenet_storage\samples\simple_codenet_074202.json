{"sample_id": "simple_codenet_074202", "dataset_name": "simple_codenet", "problem_id": "p14841", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14841", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074202_func_solve"], "timestamp": 1754139062.6393418}