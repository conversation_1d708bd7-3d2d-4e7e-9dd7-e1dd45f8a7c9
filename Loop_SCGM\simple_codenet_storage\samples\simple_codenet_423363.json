{"sample_id": "simple_codenet_423363", "dataset_name": "simple_codenet", "problem_id": "p84673", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p84673", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_423363_func_solve"], "timestamp": 1754139744.5425282}