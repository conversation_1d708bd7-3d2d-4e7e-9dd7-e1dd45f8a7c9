{"sample_id": "simple_codenet_194020", "dataset_name": "simple_codenet", "problem_id": "p38805", "code": "#include <iostream>\nusing namespace std;\nint main() {\n    int n; cin >> n;\n    cout << n * (n + 1) / 2 << endl;\n    return 0;\n}", "language": "c++", "problem_description": "CodeNet problem p38805", "test_cases": "", "libraries": ["iostream"], "difficulty": "Medium", "tags": ["codenet", "c++"], "patterns": ["simple_codenet_194020_func_main"], "timestamp": 1754139318.1513603}