{"sample_id": "simple_codenet_140079", "dataset_name": "simple_codenet", "problem_id": "p28016", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p28016", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_140079_func_solve"], "timestamp": 1754139213.087373}