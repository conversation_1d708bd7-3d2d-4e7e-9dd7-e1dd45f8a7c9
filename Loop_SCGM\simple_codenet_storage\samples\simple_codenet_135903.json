{"sample_id": "simple_codenet_135903", "dataset_name": "simple_codenet", "problem_id": "p27181", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27181", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135903_func_solve"], "timestamp": 1754139203.6368513}