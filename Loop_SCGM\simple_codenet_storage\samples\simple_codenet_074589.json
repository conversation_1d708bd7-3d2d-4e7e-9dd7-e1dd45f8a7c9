{"sample_id": "simple_codenet_074589", "dataset_name": "simple_codenet", "problem_id": "p14918", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14918", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074589_func_solve"], "timestamp": 1754139063.484429}