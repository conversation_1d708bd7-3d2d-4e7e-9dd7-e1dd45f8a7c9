{"sample_id": "simple_codenet_140049", "dataset_name": "simple_codenet", "problem_id": "p28010", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p28010", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_140049_func_solve"], "timestamp": 1754139213.0004354}