{"sample_id": "simple_codenet_155933", "dataset_name": "massive_codenet", "problem_id": "p31187", "code": "#include <vector>\n#include <algorithm>\nusing namespace std;\nint main() {\n    vector<int> v = {3, 1, 4, 1, 5};\n    sort(v.begin(), v.end());\n    return 0;\n}", "language": "c++", "problem_description": "CodeNet problem p31187", "test_cases": "", "libraries": ["vector", "algorithm"], "difficulty": "Hard", "tags": ["codenet", "c++"], "patterns": [], "timestamp": 1754148016.249873}