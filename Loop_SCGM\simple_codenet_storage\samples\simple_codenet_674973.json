{"sample_id": "simple_codenet_674973", "dataset_name": "simple_codenet", "problem_id": "p134995", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p134995", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_674973_func_solve"], "timestamp": 1754140222.0438595}