{"sample_id": "simple_codenet_423117", "dataset_name": "simple_codenet", "problem_id": "p84624", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p84624", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_423117_func_solve"], "timestamp": 1754139743.7800293}