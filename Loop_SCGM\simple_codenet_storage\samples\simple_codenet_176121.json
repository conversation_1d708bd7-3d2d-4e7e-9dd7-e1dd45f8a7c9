{"sample_id": "simple_codenet_176121", "dataset_name": "simple_codenet", "problem_id": "p35225", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35225", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_176121_func_solve"], "timestamp": 1754139285.818012}