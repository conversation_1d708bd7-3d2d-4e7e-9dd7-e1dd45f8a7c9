{"sample_id": "simple_codenet_352128", "dataset_name": "massive_codenet", "problem_id": "p70426", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p70426", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_352128_func_solve"], "timestamp": 1754147961.7266943}