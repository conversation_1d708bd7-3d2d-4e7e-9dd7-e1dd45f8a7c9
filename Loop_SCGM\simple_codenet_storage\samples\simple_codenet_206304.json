{"sample_id": "simple_codenet_206304", "dataset_name": "massive_codenet", "problem_id": "p41261", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p41261", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_206304_func_solve"], "timestamp": 1754148024.8674948}