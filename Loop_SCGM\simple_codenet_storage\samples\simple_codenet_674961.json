{"sample_id": "simple_codenet_674961", "dataset_name": "simple_codenet", "problem_id": "p134993", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p134993", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_674961_func_solve"], "timestamp": 1754140222.020074}