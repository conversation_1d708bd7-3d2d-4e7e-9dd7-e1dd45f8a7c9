{"sample_id": "simple_codenet_135771", "dataset_name": "simple_codenet", "problem_id": "p27155", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27155", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135771_func_solve"], "timestamp": 1754139203.3680458}