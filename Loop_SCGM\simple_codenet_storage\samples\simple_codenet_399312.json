{"sample_id": "simple_codenet_399312", "dataset_name": "simple_codenet", "problem_id": "p79863", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p79863", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_399312_func_solve"], "timestamp": 1754139677.9407983}