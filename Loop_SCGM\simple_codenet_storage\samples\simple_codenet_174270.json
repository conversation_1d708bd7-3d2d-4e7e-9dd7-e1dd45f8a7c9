{"sample_id": "simple_codenet_174270", "dataset_name": "simple_codenet", "problem_id": "p34855", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p34855", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_174270_func_solve"], "timestamp": 1754139282.9344642}