{"sample_id": "simple_codenet_194157", "dataset_name": "simple_codenet", "problem_id": "p38832", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p38832", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_194157_func_solve"], "timestamp": 1754139318.3587635}