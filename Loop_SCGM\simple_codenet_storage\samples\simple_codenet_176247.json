{"sample_id": "simple_codenet_176247", "dataset_name": "simple_codenet", "problem_id": "p35250", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35250", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_176247_func_solve"], "timestamp": 1754139286.0361514}