{"sample_id": "simple_codenet_327387", "dataset_name": "simple_codenet", "problem_id": "p65478", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p65478", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_327387_func_solve"], "timestamp": 1754139536.3212655}