{"sample_id": "simple_codenet_300300", "dataset_name": "massive_codenet", "problem_id": "p60061", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p60061", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_300300_func_solve"], "timestamp": 1754147937.9173758}