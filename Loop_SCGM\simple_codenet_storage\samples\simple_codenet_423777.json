{"sample_id": "simple_codenet_423777", "dataset_name": "simple_codenet", "problem_id": "p84756", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p84756", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_423777_func_solve"], "timestamp": 1754139745.5840287}