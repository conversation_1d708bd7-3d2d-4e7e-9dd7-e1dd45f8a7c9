{"sample_id": "simple_codenet_675000", "dataset_name": "simple_codenet", "problem_id": "p135001", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p135001", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_675000_func_solve"], "timestamp": 1754140222.0983727}