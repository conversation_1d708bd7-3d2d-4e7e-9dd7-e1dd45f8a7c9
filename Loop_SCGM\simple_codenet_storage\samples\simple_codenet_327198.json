{"sample_id": "simple_codenet_327198", "dataset_name": "simple_codenet", "problem_id": "p65440", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p65440", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_327198_func_solve"], "timestamp": 1754139535.8533368}