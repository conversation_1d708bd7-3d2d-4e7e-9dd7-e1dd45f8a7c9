{"sample_id": "simple_codenet_135735", "dataset_name": "simple_codenet", "problem_id": "p27148", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27148", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135735_func_solve"], "timestamp": 1754139203.2962148}