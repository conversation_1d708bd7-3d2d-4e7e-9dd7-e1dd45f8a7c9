{"sample_id": "simple_codenet_399243", "dataset_name": "simple_codenet", "problem_id": "p79849", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p79849", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_399243_func_solve"], "timestamp": 1754139677.8331597}