{"sample_id": "simple_codenet_639807", "dataset_name": "simple_codenet", "problem_id": "p127962", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p127962", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_639807_func_solve"], "timestamp": 1754140157.3998582}