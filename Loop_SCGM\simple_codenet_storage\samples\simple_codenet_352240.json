{"sample_id": "simple_codenet_352240", "dataset_name": "massive_codenet", "problem_id": "p70449", "code": "def fibon<PERSON>ci(n):\n    if n <= 1: return n\n    return fi<PERSON><PERSON><PERSON>(n-1) + <PERSON><PERSON><PERSON><PERSON>(n-2)", "language": "python", "problem_description": "CodeNet problem p70449", "test_cases": "", "libraries": [], "difficulty": "Medium", "tags": ["codenet", "python"], "patterns": ["simple_codenet_352240_func_<PERSON><PERSON><PERSON><PERSON>"], "timestamp": 1754147963.4337637}