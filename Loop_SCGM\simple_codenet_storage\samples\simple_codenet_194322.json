{"sample_id": "simple_codenet_194322", "dataset_name": "simple_codenet", "problem_id": "p38865", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p38865", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_194322_func_solve"], "timestamp": 1754139318.7004046}