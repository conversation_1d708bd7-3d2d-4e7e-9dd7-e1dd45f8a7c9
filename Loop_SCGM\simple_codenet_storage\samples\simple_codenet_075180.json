{"sample_id": "simple_codenet_075180", "dataset_name": "simple_codenet", "problem_id": "p15037", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p15037", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_075180_func_solve"], "timestamp": 1754139064.7134345}