{"sample_id": "simple_codenet_423618", "dataset_name": "simple_codenet", "problem_id": "p84724", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p84724", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_423618_func_solve"], "timestamp": 1754139745.1284459}