{"pattern_id": "simple_codenet_658917_func_solve", "pattern_type": "function", "language": "python", "code_snippet": "def solve():\n    n = int(input())\n    return sum(range(n))", "symbolic_form": "def solve(...)", "complexity": "O(n)", "libraries": [], "domain": "algorithms", "test_cases": [], "metadata": {"type": "function", "name": "solve", "language": "python", "symbolic_form": "def solve(...)", "complexity": "O(n)"}, "timestamp": 1754140192.4492674}