{"sample_id": "simple_codenet_194295", "dataset_name": "simple_codenet", "problem_id": "p38860", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p38860", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_194295_func_solve"], "timestamp": 1754139318.645541}