{"sample_id": "simple_codenet_176742", "dataset_name": "simple_codenet", "problem_id": "p35349", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35349", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_176742_func_solve"], "timestamp": 1754139286.8425415}