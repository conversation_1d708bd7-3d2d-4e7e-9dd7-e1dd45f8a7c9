{"sample_id": "simple_codenet_053676", "dataset_name": "massive_codenet", "problem_id": "p10736", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p10736", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_053676_func_solve"], "timestamp": 1754147985.1429942}