{"sample_id": "simple_codenet_327087", "dataset_name": "simple_codenet", "problem_id": "p65418", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p65418", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_327087_func_solve"], "timestamp": 1754139535.6544826}