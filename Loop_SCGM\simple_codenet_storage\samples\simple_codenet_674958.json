{"sample_id": "simple_codenet_674958", "dataset_name": "simple_codenet", "problem_id": "p134992", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p134992", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_674958_func_solve"], "timestamp": 1754140222.0120215}