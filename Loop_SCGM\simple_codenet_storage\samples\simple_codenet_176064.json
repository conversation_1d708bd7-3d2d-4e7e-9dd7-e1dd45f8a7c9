{"sample_id": "simple_codenet_176064", "dataset_name": "simple_codenet", "problem_id": "p35213", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35213", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_176064_func_solve"], "timestamp": 1754139285.7195644}