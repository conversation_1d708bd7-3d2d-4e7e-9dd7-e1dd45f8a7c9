{"sample_id": "simple_codenet_174406", "dataset_name": "simple_codenet", "problem_id": "p34882", "code": "#include <iostream>\nusing namespace std;\nint main() {\n    int n; cin >> n;\n    cout << n * (n + 1) / 2 << endl;\n    return 0;\n}", "language": "c++", "problem_description": "CodeNet problem p34882", "test_cases": "", "libraries": ["iostream"], "difficulty": "Medium", "tags": ["codenet", "c++"], "patterns": ["simple_codenet_174406_func_main"], "timestamp": 1754139283.1528387}