{"sample_id": "simple_codenet_194313", "dataset_name": "simple_codenet", "problem_id": "p38863", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p38863", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_194313_func_solve"], "timestamp": 1754139318.6803017}