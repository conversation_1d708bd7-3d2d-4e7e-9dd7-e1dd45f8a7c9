{"sample_id": "simple_codenet_053314", "dataset_name": "massive_codenet", "problem_id": "p10663", "code": "import java.util.*;\npublic class Solution {\n    public static void main(String[] args) {\n        Scanner sc = new Scanner(System.in);\n        int n = sc.nextInt();\n        System.out.println(n * (n + 1) / 2);\n    }\n}", "language": "java", "problem_description": "CodeNet problem p10663", "test_cases": "", "libraries": ["util"], "difficulty": "Medium", "tags": ["codenet", "java"], "patterns": ["simple_codenet_053314_func_Scanner"], "timestamp": 1754147979.7152145}