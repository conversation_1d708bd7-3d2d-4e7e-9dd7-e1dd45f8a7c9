{"sample_id": "simple_codenet_135072", "dataset_name": "simple_codenet", "problem_id": "p27015", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27015", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135072_func_solve"], "timestamp": 1754139201.7318587}