{"sample_id": "simple_codenet_176160", "dataset_name": "simple_codenet", "problem_id": "p35233", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35233", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_176160_func_solve"], "timestamp": 1754139285.872972}