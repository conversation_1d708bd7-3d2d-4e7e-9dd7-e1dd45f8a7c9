{"sample_id": "simple_codenet_327222", "dataset_name": "simple_codenet", "problem_id": "p65445", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p65445", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_327222_func_solve"], "timestamp": 1754139535.8982317}