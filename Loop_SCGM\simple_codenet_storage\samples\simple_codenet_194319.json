{"sample_id": "simple_codenet_194319", "dataset_name": "simple_codenet", "problem_id": "p38864", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p38864", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_194319_func_solve"], "timestamp": 1754139318.6950428}