{"sample_id": "simple_codenet_423507", "dataset_name": "simple_codenet", "problem_id": "p84702", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p84702", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_423507_func_solve"], "timestamp": 1754139744.855514}