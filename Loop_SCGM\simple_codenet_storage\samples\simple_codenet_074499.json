{"sample_id": "simple_codenet_074499", "dataset_name": "simple_codenet", "problem_id": "p14900", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14900", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074499_func_solve"], "timestamp": 1754139063.3027136}