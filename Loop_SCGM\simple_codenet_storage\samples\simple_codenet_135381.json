{"sample_id": "simple_codenet_135381", "dataset_name": "simple_codenet", "problem_id": "p27077", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27077", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135381_func_solve"], "timestamp": 1754139202.4356968}