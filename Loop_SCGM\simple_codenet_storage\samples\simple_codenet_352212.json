{"sample_id": "simple_codenet_352212", "dataset_name": "massive_codenet", "problem_id": "p70443", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p70443", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_352212_func_solve"], "timestamp": 1754147962.967243}