{"sample_id": "simple_codenet_423303", "dataset_name": "simple_codenet", "problem_id": "p84661", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p84661", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_423303_func_solve"], "timestamp": 1754139744.3889966}