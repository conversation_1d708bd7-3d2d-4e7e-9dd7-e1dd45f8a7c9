{"sample_id": "simple_codenet_423327", "dataset_name": "simple_codenet", "problem_id": "p84666", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p84666", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_423327_func_solve"], "timestamp": 1754139744.454287}