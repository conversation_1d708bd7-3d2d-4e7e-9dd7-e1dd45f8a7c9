{"sample_id": "simple_codenet_327171", "dataset_name": "simple_codenet", "problem_id": "p65435", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p65435", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_327171_func_solve"], "timestamp": 1754139535.7787175}