{"sample_id": "simple_codenet_135636", "dataset_name": "simple_codenet", "problem_id": "p27128", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27128", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135636_func_solve"], "timestamp": 1754139203.10115}