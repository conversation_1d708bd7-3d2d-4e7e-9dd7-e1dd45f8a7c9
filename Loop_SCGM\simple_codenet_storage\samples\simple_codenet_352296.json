{"sample_id": "simple_codenet_352296", "dataset_name": "massive_codenet", "problem_id": "p70460", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p70460", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_352296_func_solve"], "timestamp": 1754147964.1615994}