{"sample_id": "simple_codenet_176832", "dataset_name": "simple_codenet", "problem_id": "p35367", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35367", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_176832_func_solve"], "timestamp": 1754139286.985625}