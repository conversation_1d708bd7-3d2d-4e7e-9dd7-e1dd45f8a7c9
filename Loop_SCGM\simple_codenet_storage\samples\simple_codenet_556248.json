{"sample_id": "simple_codenet_556248", "dataset_name": "massive_codenet", "problem_id": "p111250", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p111250", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_556248_func_solve"], "timestamp": 1754148025.268026}