{"sample_id": "simple_codenet_639861", "dataset_name": "simple_codenet", "problem_id": "p127973", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p127973", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_639861_func_solve"], "timestamp": 1754140157.4938595}