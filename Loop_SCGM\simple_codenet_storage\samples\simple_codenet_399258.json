{"sample_id": "simple_codenet_399258", "dataset_name": "simple_codenet", "problem_id": "p79852", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p79852", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_399258_func_solve"], "timestamp": 1754139677.858797}