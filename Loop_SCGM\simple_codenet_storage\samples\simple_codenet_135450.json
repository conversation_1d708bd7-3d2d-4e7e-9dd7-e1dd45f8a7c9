{"sample_id": "simple_codenet_135450", "dataset_name": "simple_codenet", "problem_id": "p27091", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27091", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135450_func_solve"], "timestamp": 1754139202.6445663}