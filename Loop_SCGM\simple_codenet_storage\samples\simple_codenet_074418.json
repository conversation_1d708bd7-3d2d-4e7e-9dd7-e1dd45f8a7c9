{"sample_id": "simple_codenet_074418", "dataset_name": "simple_codenet", "problem_id": "p14884", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14884", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074418_func_solve"], "timestamp": 1754139063.1525173}