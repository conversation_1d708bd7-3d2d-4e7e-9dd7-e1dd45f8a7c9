{"sample_id": "simple_codenet_176388", "dataset_name": "simple_codenet", "problem_id": "p35278", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35278", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_176388_func_solve"], "timestamp": 1754139286.273744}