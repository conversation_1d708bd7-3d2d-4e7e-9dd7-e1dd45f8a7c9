{"sample_id": "simple_codenet_206640", "dataset_name": "massive_codenet", "problem_id": "p41329", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p41329", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_206640_func_solve"], "timestamp": 1754148029.938255}