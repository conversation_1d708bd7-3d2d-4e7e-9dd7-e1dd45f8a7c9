{"sample_id": "simple_codenet_480264", "dataset_name": "simple_codenet", "problem_id": "p96053", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p96053", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_480264_func_solve"], "timestamp": 1754139869.876967}