{"sample_id": "simple_codenet_352548", "dataset_name": "massive_codenet", "problem_id": "p70510", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p70510", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_352548_func_solve"], "timestamp": 1754147967.452992}