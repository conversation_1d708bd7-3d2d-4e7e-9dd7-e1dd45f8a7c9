{"sample_id": "simple_codenet_639831", "dataset_name": "simple_codenet", "problem_id": "p127967", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p127967", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_639831_func_solve"], "timestamp": 1754140157.4364533}