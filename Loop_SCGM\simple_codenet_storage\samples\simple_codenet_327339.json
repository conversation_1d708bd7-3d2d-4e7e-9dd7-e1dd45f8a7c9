{"sample_id": "simple_codenet_327339", "dataset_name": "simple_codenet", "problem_id": "p65468", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p65468", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_327339_func_solve"], "timestamp": 1754139536.199321}