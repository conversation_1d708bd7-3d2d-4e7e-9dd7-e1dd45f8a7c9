{"sample_id": "simple_codenet_423057", "dataset_name": "simple_codenet", "problem_id": "p84612", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p84612", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_423057_func_solve"], "timestamp": 1754139743.585855}