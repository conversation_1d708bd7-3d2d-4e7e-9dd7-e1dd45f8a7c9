{"sample_id": "simple_codenet_140064", "dataset_name": "simple_codenet", "problem_id": "p28013", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p28013", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_140064_func_solve"], "timestamp": 1754139213.053674}