{"sample_id": "simple_codenet_176157", "dataset_name": "simple_codenet", "problem_id": "p35232", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35232", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_176157_func_solve"], "timestamp": 1754139285.869667}