{"sample_id": "simple_codenet_173890", "dataset_name": "simple_codenet", "problem_id": "p34779", "code": "#include <iostream>\nusing namespace std;\nint main() {\n    int n; cin >> n;\n    cout << n * (n + 1) / 2 << endl;\n    return 0;\n}", "language": "c++", "problem_description": "CodeNet problem p34779", "test_cases": "", "libraries": ["iostream"], "difficulty": "Medium", "tags": ["codenet", "c++"], "patterns": ["simple_codenet_173890_func_main"], "timestamp": 1754139282.3338869}