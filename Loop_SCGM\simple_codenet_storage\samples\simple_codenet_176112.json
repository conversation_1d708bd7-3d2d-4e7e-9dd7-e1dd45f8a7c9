{"sample_id": "simple_codenet_176112", "dataset_name": "simple_codenet", "problem_id": "p35223", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35223", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_176112_func_solve"], "timestamp": 1754139285.7997131}