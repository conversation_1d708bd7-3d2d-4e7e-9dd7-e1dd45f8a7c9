{"sample_id": "simple_codenet_327105", "dataset_name": "simple_codenet", "problem_id": "p65422", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p65422", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_327105_func_solve"], "timestamp": 1754139535.6782875}