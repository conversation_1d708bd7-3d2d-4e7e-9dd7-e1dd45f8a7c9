{"sample_id": "simple_codenet_194154", "dataset_name": "simple_codenet", "problem_id": "p38831", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p38831", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_194154_func_solve"], "timestamp": 1754139318.3541422}