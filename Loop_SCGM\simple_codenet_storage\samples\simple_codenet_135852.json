{"sample_id": "simple_codenet_135852", "dataset_name": "simple_codenet", "problem_id": "p27171", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27171", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135852_func_solve"], "timestamp": 1754139203.5387268}