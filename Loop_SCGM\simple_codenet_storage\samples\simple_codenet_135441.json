{"sample_id": "simple_codenet_135441", "dataset_name": "simple_codenet", "problem_id": "p27089", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27089", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135441_func_solve"], "timestamp": 1754139202.5960164}