{"sample_id": "simple_codenet_206052", "dataset_name": "massive_codenet", "problem_id": "p41211", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p41211", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_206052_func_solve"], "timestamp": 1754148018.3033814}