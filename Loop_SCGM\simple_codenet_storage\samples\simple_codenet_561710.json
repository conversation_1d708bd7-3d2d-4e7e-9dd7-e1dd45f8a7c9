{"sample_id": "simple_codenet_561710", "dataset_name": "massive_codenet", "problem_id": "p112343", "code": "import java.util.*;\npublic class Solution {\n    public static void main(String[] args) {\n        Scanner sc = new Scanner(System.in);\n        int n = sc.nextInt();\n        System.out.println(n * (n + 1) / 2);\n    }\n}", "language": "java", "problem_description": "CodeNet problem p112343", "test_cases": "", "libraries": ["util"], "difficulty": "Hard", "tags": ["codenet", "java"], "patterns": ["simple_codenet_561710_func_Scanner"], "timestamp": 1754148108.3491387}