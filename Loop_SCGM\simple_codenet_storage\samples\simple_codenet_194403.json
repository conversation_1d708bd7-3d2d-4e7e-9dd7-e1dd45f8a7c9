{"sample_id": "simple_codenet_194403", "dataset_name": "simple_codenet", "problem_id": "p38881", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p38881", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_194403_func_solve"], "timestamp": 1754139318.8685102}