{"sample_id": "simple_codenet_399249", "dataset_name": "simple_codenet", "problem_id": "p79850", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p79850", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_399249_func_solve"], "timestamp": 1754139677.8450356}