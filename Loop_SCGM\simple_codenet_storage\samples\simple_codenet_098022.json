{"sample_id": "simple_codenet_098022", "dataset_name": "simple_codenet", "problem_id": "p19605", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p19605", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_098022_func_solve"], "timestamp": 1754139116.4200294}