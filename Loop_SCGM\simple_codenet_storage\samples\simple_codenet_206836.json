{"sample_id": "simple_codenet_206836", "dataset_name": "massive_codenet", "problem_id": "p41368", "code": "def fibon<PERSON>ci(n):\n    if n <= 1: return n\n    return fi<PERSON><PERSON><PERSON>(n-1) + <PERSON><PERSON><PERSON><PERSON>(n-2)", "language": "python", "problem_description": "CodeNet problem p41368", "test_cases": "", "libraries": [], "difficulty": "Medium", "tags": ["codenet", "python"], "patterns": ["simple_codenet_206836_func_<PERSON><PERSON><PERSON><PERSON>"], "timestamp": 1754148032.8334825}