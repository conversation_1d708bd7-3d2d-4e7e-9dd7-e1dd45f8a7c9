{"sample_id": "simple_codenet_674718", "dataset_name": "simple_codenet", "problem_id": "p134944", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p134944", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_674718_func_solve"], "timestamp": 1754140221.5057137}