{"sample_id": "simple_codenet_173882", "dataset_name": "simple_codenet", "problem_id": "p34777", "code": "import java.util.*;\npublic class Solution {\n    public static void main(String[] args) {\n        Scanner sc = new Scanner(System.in);\n        int n = sc.nextInt();\n        System.out.println(n * (n + 1) / 2);\n    }\n}", "language": "java", "problem_description": "CodeNet problem p34777", "test_cases": "", "libraries": ["util"], "difficulty": "Hard", "tags": ["codenet", "java"], "patterns": ["simple_codenet_173882_method_main", "simple_codenet_173882_method_Scanner", "simple_codenet_173882_class_Solution"], "timestamp": 1754139282.3206635}