{"sample_id": "simple_codenet_674979", "dataset_name": "simple_codenet", "problem_id": "p134996", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p134996", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_674979_func_solve"], "timestamp": 1754140222.0556033}