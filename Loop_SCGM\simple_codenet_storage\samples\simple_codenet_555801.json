{"sample_id": "simple_codenet_555801", "dataset_name": "massive_codenet", "problem_id": "p111161", "code": "#include <vector>\n#include <algorithm>\nusing namespace std;\nint main() {\n    vector<int> v = {3, 1, 4, 1, 5};\n    sort(v.begin(), v.end());\n    return 0;\n}", "language": "c++", "problem_description": "CodeNet problem p111161", "test_cases": "", "libraries": ["vector", "algorithm"], "difficulty": "Easy", "tags": ["codenet", "c++"], "patterns": [], "timestamp": 1754148016.0157192}