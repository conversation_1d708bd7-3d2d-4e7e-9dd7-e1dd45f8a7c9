{"sample_id": "simple_codenet_135978", "dataset_name": "simple_codenet", "problem_id": "p27196", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27196", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135978_func_solve"], "timestamp": 1754139203.8004494}