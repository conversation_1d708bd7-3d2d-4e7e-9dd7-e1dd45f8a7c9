{"sample_id": "simple_codenet_053424", "dataset_name": "massive_codenet", "problem_id": "p10685", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p10685", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_053424_func_solve"], "timestamp": 1754147981.3804567}