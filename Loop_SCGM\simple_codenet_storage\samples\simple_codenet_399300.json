{"sample_id": "simple_codenet_399300", "dataset_name": "simple_codenet", "problem_id": "p79861", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p79861", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_399300_func_solve"], "timestamp": 1754139677.920085}