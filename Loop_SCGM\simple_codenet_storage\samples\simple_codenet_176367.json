{"sample_id": "simple_codenet_176367", "dataset_name": "simple_codenet", "problem_id": "p35274", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35274", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_176367_func_solve"], "timestamp": 1754139286.2323933}