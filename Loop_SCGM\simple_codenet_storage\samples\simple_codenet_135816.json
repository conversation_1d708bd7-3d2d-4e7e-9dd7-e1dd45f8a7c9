{"sample_id": "simple_codenet_135816", "dataset_name": "simple_codenet", "problem_id": "p27164", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27164", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135816_func_solve"], "timestamp": 1754139203.4469728}