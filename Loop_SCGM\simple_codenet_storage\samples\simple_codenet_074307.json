{"sample_id": "simple_codenet_074307", "dataset_name": "simple_codenet", "problem_id": "p14862", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14862", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074307_func_solve"], "timestamp": 1754139062.8884206}