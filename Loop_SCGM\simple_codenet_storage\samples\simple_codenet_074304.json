{"sample_id": "simple_codenet_074304", "dataset_name": "simple_codenet", "problem_id": "p14861", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14861", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074304_func_solve"], "timestamp": 1754139062.8800857}