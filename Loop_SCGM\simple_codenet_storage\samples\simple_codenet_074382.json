{"sample_id": "simple_codenet_074382", "dataset_name": "simple_codenet", "problem_id": "p14877", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14877", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074382_func_solve"], "timestamp": 1754139063.089273}