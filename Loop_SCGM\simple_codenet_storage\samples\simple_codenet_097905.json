{"sample_id": "simple_codenet_097905", "dataset_name": "simple_codenet", "problem_id": "p19582", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p19582", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_097905_func_solve"], "timestamp": 1754139116.1806602}