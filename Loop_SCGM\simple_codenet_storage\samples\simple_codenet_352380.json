{"sample_id": "simple_codenet_352380", "dataset_name": "massive_codenet", "problem_id": "p70477", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p70477", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_352380_func_solve"], "timestamp": 1754147965.2393498}