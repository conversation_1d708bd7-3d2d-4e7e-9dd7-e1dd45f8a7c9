{"sample_id": "simple_codenet_399324", "dataset_name": "simple_codenet", "problem_id": "p79865", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p79865", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_399324_func_solve"], "timestamp": 1754139677.9624968}