{"sample_id": "simple_codenet_176826", "dataset_name": "simple_codenet", "problem_id": "p35366", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35366", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_176826_func_solve"], "timestamp": 1754139286.9772642}