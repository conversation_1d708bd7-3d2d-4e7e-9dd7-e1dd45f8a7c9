{"sample_id": "simple_codenet_674674", "dataset_name": "simple_codenet", "problem_id": "p134935", "code": "#include <iostream>\nusing namespace std;\nint main() {\n    int n; cin >> n;\n    cout << n * (n + 1) / 2 << endl;\n    return 0;\n}", "language": "c++", "problem_description": "CodeNet problem p134935", "test_cases": "", "libraries": ["iostream"], "difficulty": "Medium", "tags": ["codenet", "c++"], "patterns": [], "timestamp": 1754140221.4125857}