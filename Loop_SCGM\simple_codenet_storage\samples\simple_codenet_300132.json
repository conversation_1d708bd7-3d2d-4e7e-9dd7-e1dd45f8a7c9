{"sample_id": "simple_codenet_300132", "dataset_name": "massive_codenet", "problem_id": "p60027", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p60027", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_300132_func_solve"], "timestamp": 1754147935.6168752}