{"sample_id": "simple_codenet_098115", "dataset_name": "simple_codenet", "problem_id": "p19624", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p19624", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_098115_func_solve"], "timestamp": 1754139116.6210163}