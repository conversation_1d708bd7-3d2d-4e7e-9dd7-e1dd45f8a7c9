{"sample_id": "simple_codenet_075849", "dataset_name": "simple_codenet", "problem_id": "p15170", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p15170", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_075849_func_solve"], "timestamp": 1754139066.1722605}