{"sample_id": "simple_codenet_556196", "dataset_name": "massive_codenet", "problem_id": "p111240", "code": "function solve(n) {\n    return Array.from({length: n}, (_, i) => i).reduce((a, b) => a + b, 0);\n}", "language": "javascript", "problem_description": "CodeNet problem p111240", "test_cases": "", "libraries": [], "difficulty": "Hard", "tags": ["codenet", "javascript"], "patterns": [], "timestamp": 1754148023.5045562}