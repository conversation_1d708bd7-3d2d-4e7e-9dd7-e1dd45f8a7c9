{"sample_id": "simple_codenet_399354", "dataset_name": "simple_codenet", "problem_id": "p79871", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p79871", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_399354_func_solve"], "timestamp": 1754139678.030398}