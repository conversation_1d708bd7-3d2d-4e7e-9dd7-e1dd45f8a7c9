{"sample_id": "simple_codenet_674811", "dataset_name": "simple_codenet", "problem_id": "p134963", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p134963", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_674811_func_solve"], "timestamp": 1754140221.6986525}