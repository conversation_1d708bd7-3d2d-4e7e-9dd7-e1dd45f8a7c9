{"sample_id": "simple_codenet_176274", "dataset_name": "simple_codenet", "problem_id": "p35255", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35255", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_176274_func_solve"], "timestamp": 1754139286.081158}