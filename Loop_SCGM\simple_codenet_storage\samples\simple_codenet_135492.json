{"sample_id": "simple_codenet_135492", "dataset_name": "simple_codenet", "problem_id": "p27099", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27099", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135492_func_solve"], "timestamp": 1754139202.743281}