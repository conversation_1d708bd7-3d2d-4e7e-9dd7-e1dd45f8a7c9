{"sample_id": "simple_codenet_176541", "dataset_name": "simple_codenet", "problem_id": "p35309", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35309", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_176541_func_solve"], "timestamp": 1754139286.5430348}