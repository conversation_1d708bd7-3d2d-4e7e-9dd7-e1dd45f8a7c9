{"sample_id": "simple_codenet_399072", "dataset_name": "simple_codenet", "problem_id": "p79815", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p79815", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_399072_func_solve"], "timestamp": 1754139677.485027}