{"sample_id": "simple_codenet_194343", "dataset_name": "simple_codenet", "problem_id": "p38869", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p38869", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_194343_func_solve"], "timestamp": 1754139318.7391133}