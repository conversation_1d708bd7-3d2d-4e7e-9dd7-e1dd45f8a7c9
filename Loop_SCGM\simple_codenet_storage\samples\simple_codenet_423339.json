{"sample_id": "simple_codenet_423339", "dataset_name": "simple_codenet", "problem_id": "p84668", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p84668", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_423339_func_solve"], "timestamp": 1754139744.4784625}