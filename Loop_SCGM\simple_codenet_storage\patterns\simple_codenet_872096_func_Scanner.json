{"pattern_id": "simple_codenet_872096_func_Scanner", "pattern_type": "function", "language": "java", "code_snippet": "import java.util.*;\npublic class Solution {\n    public static void main(String[] args) {\n        Scanner sc = new Scanner(System.in);\n        int n = sc.nextInt();\n        System.out.println(n * (n + ", "symbolic_form": "<PERSON><PERSON><PERSON>(...)", "complexity": "O(n)", "libraries": ["util"], "domain": "advanced_algorithms", "test_cases": [], "metadata": {"type": "function", "name": "Scanner", "language": "java", "symbolic_form": "<PERSON><PERSON><PERSON>(...)", "complexity": "O(n)"}, "timestamp": 1754140588.6857562}