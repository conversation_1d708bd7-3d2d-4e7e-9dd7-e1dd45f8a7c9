{"sample_id": "simple_codenet_556500", "dataset_name": "massive_codenet", "problem_id": "p111301", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p111301", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_556500_func_solve"], "timestamp": 1754148029.3993554}