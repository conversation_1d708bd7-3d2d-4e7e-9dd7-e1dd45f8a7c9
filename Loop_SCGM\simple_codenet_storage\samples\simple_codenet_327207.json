{"sample_id": "simple_codenet_327207", "dataset_name": "simple_codenet", "problem_id": "p65442", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p65442", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_327207_func_solve"], "timestamp": 1754139535.8707068}