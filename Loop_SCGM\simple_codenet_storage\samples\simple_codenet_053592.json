{"sample_id": "simple_codenet_053592", "dataset_name": "massive_codenet", "problem_id": "p10719", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p10719", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_053592_func_solve"], "timestamp": 1754147983.943438}