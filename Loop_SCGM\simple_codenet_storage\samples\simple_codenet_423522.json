{"sample_id": "simple_codenet_423522", "dataset_name": "simple_codenet", "problem_id": "p84705", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p84705", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_423522_func_solve"], "timestamp": 1754139744.8878365}