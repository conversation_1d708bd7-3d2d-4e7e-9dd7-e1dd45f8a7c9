{"sample_id": "simple_codenet_423648", "dataset_name": "simple_codenet", "problem_id": "p84730", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p84730", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_423648_func_solve"], "timestamp": 1754139745.249435}