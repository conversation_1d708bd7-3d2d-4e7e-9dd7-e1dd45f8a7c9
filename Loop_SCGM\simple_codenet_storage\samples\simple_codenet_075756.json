{"sample_id": "simple_codenet_075756", "dataset_name": "simple_codenet", "problem_id": "p15152", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p15152", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_075756_func_solve"], "timestamp": 1754139065.996623}