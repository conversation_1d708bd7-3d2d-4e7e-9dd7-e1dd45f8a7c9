{"sample_id": "simple_codenet_639702", "dataset_name": "simple_codenet", "problem_id": "p127941", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p127941", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_639702_func_solve"], "timestamp": 1754140157.1732879}