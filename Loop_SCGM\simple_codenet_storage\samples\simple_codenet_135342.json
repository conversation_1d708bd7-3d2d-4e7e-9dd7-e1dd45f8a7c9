{"sample_id": "simple_codenet_135342", "dataset_name": "simple_codenet", "problem_id": "p27069", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27069", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135342_func_solve"], "timestamp": 1754139202.3547115}