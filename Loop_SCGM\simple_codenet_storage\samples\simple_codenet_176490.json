{"sample_id": "simple_codenet_176490", "dataset_name": "simple_codenet", "problem_id": "p35299", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35299", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_176490_func_solve"], "timestamp": 1754139286.459942}