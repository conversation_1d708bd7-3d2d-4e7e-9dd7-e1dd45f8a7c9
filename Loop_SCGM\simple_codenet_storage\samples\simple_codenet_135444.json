{"sample_id": "simple_codenet_135444", "dataset_name": "simple_codenet", "problem_id": "p27089", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27089", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135444_func_solve"], "timestamp": 1754139202.6024358}