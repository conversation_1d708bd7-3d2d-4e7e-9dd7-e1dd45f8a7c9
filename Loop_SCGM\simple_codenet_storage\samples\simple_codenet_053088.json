{"sample_id": "simple_codenet_053088", "dataset_name": "massive_codenet", "problem_id": "p10618", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p10618", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_053088_func_solve"], "timestamp": 1754147976.6709187}