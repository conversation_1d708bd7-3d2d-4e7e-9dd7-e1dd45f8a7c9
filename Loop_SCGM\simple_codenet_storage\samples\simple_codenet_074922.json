{"sample_id": "simple_codenet_074922", "dataset_name": "simple_codenet", "problem_id": "p14985", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14985", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074922_func_solve"], "timestamp": 1754139064.1035297}