{"sample_id": "simple_codenet_073845", "dataset_name": "simple_codenet", "problem_id": "p14770", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14770", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_073845_func_solve"], "timestamp": 1754139061.9540443}