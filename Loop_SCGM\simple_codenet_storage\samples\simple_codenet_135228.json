{"sample_id": "simple_codenet_135228", "dataset_name": "simple_codenet", "problem_id": "p27046", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27046", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135228_func_solve"], "timestamp": 1754139202.104426}