{"sample_id": "simple_codenet_206754", "dataset_name": "massive_codenet", "problem_id": "p41351", "code": "import java.util.*;\npublic class Solution {\n    public static void main(String[] args) {\n        Scanner sc = new Scanner(System.in);\n        int n = sc.nextInt();\n        System.out.println(n * (n + 1) / 2);\n    }\n}", "language": "java", "problem_description": "CodeNet problem p41351", "test_cases": "", "libraries": ["util"], "difficulty": "Easy", "tags": ["codenet", "java"], "patterns": ["simple_codenet_206754_func_Scanner"], "timestamp": 1754148031.5602918}