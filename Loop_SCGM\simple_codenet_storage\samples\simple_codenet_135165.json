{"sample_id": "simple_codenet_135165", "dataset_name": "simple_codenet", "problem_id": "p27034", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27034", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135165_func_solve"], "timestamp": 1754139201.96583}