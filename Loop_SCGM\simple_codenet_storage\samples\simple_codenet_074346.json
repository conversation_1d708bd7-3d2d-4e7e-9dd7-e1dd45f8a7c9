{"sample_id": "simple_codenet_074346", "dataset_name": "simple_codenet", "problem_id": "p14870", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14870", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074346_func_solve"], "timestamp": 1754139062.9902024}