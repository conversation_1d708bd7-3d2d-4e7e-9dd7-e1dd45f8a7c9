{"sample_id": "simple_codenet_052926", "dataset_name": "simple_codenet", "problem_id": "p10586", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p10586", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_052926_func_solve"], "timestamp": 1754139018.2538404}