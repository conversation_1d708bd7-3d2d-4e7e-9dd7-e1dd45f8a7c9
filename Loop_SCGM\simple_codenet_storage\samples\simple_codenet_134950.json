{"sample_id": "simple_codenet_134950", "dataset_name": "simple_codenet", "problem_id": "p26991", "code": "#include <iostream>\nusing namespace std;\nint main() {\n    int n; cin >> n;\n    cout << n * (n + 1) / 2 << endl;\n    return 0;\n}", "language": "c++", "problem_description": "CodeNet problem p26991", "test_cases": "", "libraries": ["iostream"], "difficulty": "Medium", "tags": ["codenet", "c++"], "patterns": ["simple_codenet_134950_func_main"], "timestamp": 1754139201.452583}