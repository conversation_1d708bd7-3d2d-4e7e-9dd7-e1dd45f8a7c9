{"sample_id": "simple_codenet_399208", "dataset_name": "simple_codenet", "problem_id": "p79842", "code": "#include <iostream>\nusing namespace std;\nint main() {\n    int n; cin >> n;\n    cout << n * (n + 1) / 2 << endl;\n    return 0;\n}", "language": "c++", "problem_description": "CodeNet problem p79842", "test_cases": "", "libraries": ["iostream"], "difficulty": "Medium", "tags": ["codenet", "c++"], "patterns": [], "timestamp": 1754139677.746176}