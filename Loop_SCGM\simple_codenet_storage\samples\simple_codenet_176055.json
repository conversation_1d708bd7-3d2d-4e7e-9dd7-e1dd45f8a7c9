{"sample_id": "simple_codenet_176055", "dataset_name": "simple_codenet", "problem_id": "p35212", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35212", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_176055_func_solve"], "timestamp": 1754139285.7064795}