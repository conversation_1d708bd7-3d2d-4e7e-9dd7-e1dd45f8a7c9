{"sample_id": "simple_codenet_174106", "dataset_name": "simple_codenet", "problem_id": "p34822", "code": "#include <iostream>\nusing namespace std;\nint main() {\n    int n; cin >> n;\n    cout << n * (n + 1) / 2 << endl;\n    return 0;\n}", "language": "c++", "problem_description": "CodeNet problem p34822", "test_cases": "", "libraries": ["iostream"], "difficulty": "Medium", "tags": ["codenet", "c++"], "patterns": ["simple_codenet_174106_func_main"], "timestamp": 1754139282.6653547}