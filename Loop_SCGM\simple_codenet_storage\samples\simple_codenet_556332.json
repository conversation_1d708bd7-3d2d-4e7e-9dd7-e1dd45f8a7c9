{"sample_id": "simple_codenet_556332", "dataset_name": "massive_codenet", "problem_id": "p111267", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p111267", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_556332_func_solve"], "timestamp": 1754148026.6514463}