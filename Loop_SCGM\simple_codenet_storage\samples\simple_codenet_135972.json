{"sample_id": "simple_codenet_135972", "dataset_name": "simple_codenet", "problem_id": "p27195", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27195", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135972_func_solve"], "timestamp": 1754139203.7901802}