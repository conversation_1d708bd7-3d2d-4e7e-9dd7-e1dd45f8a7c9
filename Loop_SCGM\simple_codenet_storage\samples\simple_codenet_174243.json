{"sample_id": "simple_codenet_174243", "dataset_name": "simple_codenet", "problem_id": "p34849", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p34849", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_174243_func_solve"], "timestamp": 1754139282.8961127}