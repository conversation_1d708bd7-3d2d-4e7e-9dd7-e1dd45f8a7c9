{"sample_id": "simple_codenet_135942", "dataset_name": "simple_codenet", "problem_id": "p27189", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27189", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135942_func_solve"], "timestamp": 1754139203.7127113}