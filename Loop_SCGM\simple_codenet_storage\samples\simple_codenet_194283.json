{"sample_id": "simple_codenet_194283", "dataset_name": "simple_codenet", "problem_id": "p38857", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p38857", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_194283_func_solve"], "timestamp": 1754139318.6257322}