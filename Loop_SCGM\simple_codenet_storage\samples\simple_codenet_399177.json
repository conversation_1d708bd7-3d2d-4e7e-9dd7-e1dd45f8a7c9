{"sample_id": "simple_codenet_399177", "dataset_name": "simple_codenet", "problem_id": "p79836", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p79836", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_399177_func_solve"], "timestamp": 1754139677.6791399}