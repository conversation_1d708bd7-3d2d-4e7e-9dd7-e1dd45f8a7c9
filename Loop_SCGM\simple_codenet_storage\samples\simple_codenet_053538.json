{"sample_id": "simple_codenet_053538", "dataset_name": "massive_codenet", "problem_id": "p10708", "code": "import java.util.*;\npublic class Solution {\n    public static void main(String[] args) {\n        Scanner sc = new Scanner(System.in);\n        int n = sc.nextInt();\n        System.out.println(n * (n + 1) / 2);\n    }\n}", "language": "java", "problem_description": "CodeNet problem p10708", "test_cases": "", "libraries": ["util"], "difficulty": "Easy", "tags": ["codenet", "java"], "patterns": ["simple_codenet_053538_func_Scanner"], "timestamp": 1754147983.1674905}