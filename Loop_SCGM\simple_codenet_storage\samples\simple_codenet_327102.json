{"sample_id": "simple_codenet_327102", "dataset_name": "simple_codenet", "problem_id": "p65421", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p65421", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_327102_func_solve"], "timestamp": 1754139535.6721506}