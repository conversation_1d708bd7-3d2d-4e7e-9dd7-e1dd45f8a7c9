{"sample_id": "simple_codenet_399237", "dataset_name": "simple_codenet", "problem_id": "p79848", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p79848", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_399237_func_solve"], "timestamp": 1754139677.8225863}