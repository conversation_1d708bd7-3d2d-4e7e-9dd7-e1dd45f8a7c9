{"sample_id": "simple_codenet_140097", "dataset_name": "simple_codenet", "problem_id": "p28020", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p28020", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_140097_func_solve"], "timestamp": 1754139213.127843}