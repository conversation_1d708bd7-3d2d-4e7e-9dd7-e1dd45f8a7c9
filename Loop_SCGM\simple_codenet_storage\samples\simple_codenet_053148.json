{"sample_id": "simple_codenet_053148", "dataset_name": "massive_codenet", "problem_id": "p10630", "code": "function solve(n) {\n    return Array.from({length: n}, (_, i) => i).reduce((a, b) => a + b, 0);\n}", "language": "javascript", "problem_description": "CodeNet problem p10630", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "javascript"], "patterns": [], "timestamp": 1754147977.398144}