{"sample_id": "simple_codenet_423372", "dataset_name": "simple_codenet", "problem_id": "p84675", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p84675", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_423372_func_solve"], "timestamp": 1754139744.5603826}