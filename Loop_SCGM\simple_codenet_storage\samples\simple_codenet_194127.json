{"sample_id": "simple_codenet_194127", "dataset_name": "simple_codenet", "problem_id": "p38826", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p38826", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_194127_func_solve"], "timestamp": 1754139318.3165872}