{"sample_id": "simple_codenet_399111", "dataset_name": "simple_codenet", "problem_id": "p79823", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p79823", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_399111_func_solve"], "timestamp": 1754139677.5670598}