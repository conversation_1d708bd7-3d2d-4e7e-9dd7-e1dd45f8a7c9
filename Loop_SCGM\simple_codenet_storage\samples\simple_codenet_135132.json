{"sample_id": "simple_codenet_135132", "dataset_name": "simple_codenet", "problem_id": "p27027", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27027", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135132_func_solve"], "timestamp": 1754139201.8755476}