{"sample_id": "simple_codenet_352356", "dataset_name": "massive_codenet", "problem_id": "p70472", "code": "function solve(n) {\n    return Array.from({length: n}, (_, i) => i).reduce((a, b) => a + b, 0);\n}", "language": "javascript", "problem_description": "CodeNet problem p70472", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "javascript"], "patterns": [], "timestamp": 1754147964.9464762}