{"sample_id": "simple_codenet_176484", "dataset_name": "simple_codenet", "problem_id": "p35297", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35297", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_176484_func_solve"], "timestamp": 1754139286.4504528}