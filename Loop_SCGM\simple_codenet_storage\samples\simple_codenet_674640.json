{"sample_id": "simple_codenet_674640", "dataset_name": "simple_codenet", "problem_id": "p134929", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p134929", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_674640_func_solve"], "timestamp": 1754140221.3528786}