{"sample_id": "simple_codenet_674673", "dataset_name": "simple_codenet", "problem_id": "p134935", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p134935", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_674673_func_solve"], "timestamp": 1754140221.4107897}