{"sample_id": "simple_codenet_135741", "dataset_name": "simple_codenet", "problem_id": "p27149", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27149", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135741_func_solve"], "timestamp": 1754139203.3272767}