{"sample_id": "simple_codenet_399126", "dataset_name": "simple_codenet", "problem_id": "p79826", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p79826", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_399126_func_solve"], "timestamp": 1754139677.5907607}