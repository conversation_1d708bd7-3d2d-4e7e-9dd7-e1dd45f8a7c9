{"sample_id": "simple_codenet_205968", "dataset_name": "massive_codenet", "problem_id": "p41194", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p41194", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_205968_func_solve"], "timestamp": 1754148017.2346587}