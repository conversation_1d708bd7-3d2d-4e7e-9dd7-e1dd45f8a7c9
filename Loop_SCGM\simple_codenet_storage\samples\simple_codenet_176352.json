{"sample_id": "simple_codenet_176352", "dataset_name": "simple_codenet", "problem_id": "p35271", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35271", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_176352_func_solve"], "timestamp": 1754139286.207769}