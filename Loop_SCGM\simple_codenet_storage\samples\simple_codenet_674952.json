{"sample_id": "simple_codenet_674952", "dataset_name": "simple_codenet", "problem_id": "p134991", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p134991", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_674952_func_solve"], "timestamp": 1754140221.9989815}