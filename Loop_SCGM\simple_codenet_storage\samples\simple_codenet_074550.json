{"sample_id": "simple_codenet_074550", "dataset_name": "simple_codenet", "problem_id": "p14911", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14911", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074550_func_solve"], "timestamp": 1754139063.397984}