{"sample_id": "simple_codenet_399360", "dataset_name": "simple_codenet", "problem_id": "p79873", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p79873", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_399360_func_solve"], "timestamp": 1754139678.0422454}