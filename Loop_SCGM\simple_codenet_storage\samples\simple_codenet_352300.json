{"sample_id": "simple_codenet_352300", "dataset_name": "massive_codenet", "problem_id": "p70461", "code": "function solve(n) {\n    return Array.from({length: n}, (_, i) => i).reduce((a, b) => a + b, 0);\n}", "language": "javascript", "problem_description": "CodeNet problem p70461", "test_cases": "", "libraries": [], "difficulty": "Medium", "tags": ["codenet", "javascript"], "patterns": [], "timestamp": 1754147964.2177782}