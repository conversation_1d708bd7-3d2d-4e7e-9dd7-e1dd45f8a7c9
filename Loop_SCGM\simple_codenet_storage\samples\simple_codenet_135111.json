{"sample_id": "simple_codenet_135111", "dataset_name": "simple_codenet", "problem_id": "p27023", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27023", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135111_func_solve"], "timestamp": 1754139201.8281934}