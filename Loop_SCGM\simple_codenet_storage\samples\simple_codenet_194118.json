{"sample_id": "simple_codenet_194118", "dataset_name": "simple_codenet", "problem_id": "p38824", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p38824", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_194118_func_solve"], "timestamp": 1754139318.306306}