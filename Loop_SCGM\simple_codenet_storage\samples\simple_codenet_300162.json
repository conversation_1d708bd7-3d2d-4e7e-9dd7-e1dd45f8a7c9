{"sample_id": "simple_codenet_300162", "dataset_name": "massive_codenet", "problem_id": "p60033", "code": "import java.util.*;\npublic class Solution {\n    public static void main(String[] args) {\n        Scanner sc = new Scanner(System.in);\n        int n = sc.nextInt();\n        System.out.println(n * (n + 1) / 2);\n    }\n}", "language": "java", "problem_description": "CodeNet problem p60033", "test_cases": "", "libraries": ["util"], "difficulty": "Easy", "tags": ["codenet", "java"], "patterns": ["simple_codenet_300162_func_Scanner"], "timestamp": 1754147935.9970229}