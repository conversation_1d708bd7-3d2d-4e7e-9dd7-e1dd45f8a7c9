{"sample_id": "simple_codenet_075264", "dataset_name": "simple_codenet", "problem_id": "p15053", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p15053", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_075264_func_solve"], "timestamp": 1754139064.9864242}