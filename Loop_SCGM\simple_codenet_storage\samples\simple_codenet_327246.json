{"sample_id": "simple_codenet_327246", "dataset_name": "simple_codenet", "problem_id": "p65450", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p65450", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_327246_func_solve"], "timestamp": 1754139535.944639}