{"sample_id": "simple_codenet_135147", "dataset_name": "simple_codenet", "problem_id": "p27030", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27030", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135147_func_solve"], "timestamp": 1754139201.9083126}