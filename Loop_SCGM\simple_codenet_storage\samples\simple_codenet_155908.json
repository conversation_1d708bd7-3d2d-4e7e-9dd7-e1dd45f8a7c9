{"sample_id": "simple_codenet_155908", "dataset_name": "massive_codenet", "problem_id": "p31182", "code": "function solve(n) {\n    return Array.from({length: n}, (_, i) => i).reduce((a, b) => a + b, 0);\n}", "language": "javascript", "problem_description": "CodeNet problem p31182", "test_cases": "", "libraries": [], "difficulty": "Medium", "tags": ["codenet", "javascript"], "patterns": [], "timestamp": 1754148015.855507}