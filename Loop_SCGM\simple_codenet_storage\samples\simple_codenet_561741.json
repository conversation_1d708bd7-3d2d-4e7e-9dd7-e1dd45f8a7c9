{"sample_id": "simple_codenet_561741", "dataset_name": "massive_codenet", "problem_id": "p112349", "code": "package main\nimport (\n    \"fmt\"\n    \"sort\"\n)\nfunc main() {\n    arr := []int{3, 1, 4, 1, 5}\n    sort.Ints(arr)\n    fmt.Println(arr)\n}", "language": "go", "problem_description": "CodeNet problem p112349", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "go"], "patterns": [], "timestamp": 1754148108.7703516}