{"sample_id": "simple_codenet_174258", "dataset_name": "simple_codenet", "problem_id": "p34852", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p34852", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_174258_func_solve"], "timestamp": 1754139282.9106786}