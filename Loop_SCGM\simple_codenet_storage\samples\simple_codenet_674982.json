{"sample_id": "simple_codenet_674982", "dataset_name": "simple_codenet", "problem_id": "p134997", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p134997", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_674982_func_solve"], "timestamp": 1754140222.0597029}