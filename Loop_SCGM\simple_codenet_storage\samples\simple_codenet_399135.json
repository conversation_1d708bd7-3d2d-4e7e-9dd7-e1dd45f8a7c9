{"sample_id": "simple_codenet_399135", "dataset_name": "simple_codenet", "problem_id": "p79828", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p79828", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_399135_func_solve"], "timestamp": 1754139677.6005409}