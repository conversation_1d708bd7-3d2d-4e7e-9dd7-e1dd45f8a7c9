{"sample_id": "simple_codenet_174191", "dataset_name": "simple_codenet", "problem_id": "p34839", "code": "import java.util.Arrays;\npublic class ArraySort {\n    public static void main(String[] args) {\n        int[] arr = {3, 1, 4, 1, 5};\n        Arrays.sort(arr);\n    }\n}", "language": "java", "problem_description": "CodeNet problem p34839", "test_cases": "", "libraries": ["<PERSON><PERSON><PERSON>"], "difficulty": "Hard", "tags": ["codenet", "java"], "patterns": ["simple_codenet_174191_method_main", "simple_codenet_174191_class_ArraySort"], "timestamp": 1754139282.8172066}