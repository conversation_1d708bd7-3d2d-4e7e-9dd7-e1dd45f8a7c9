{"sample_id": "simple_codenet_135814", "dataset_name": "simple_codenet", "problem_id": "p27163", "code": "#include <iostream>\nusing namespace std;\nint main() {\n    int n; cin >> n;\n    cout << n * (n + 1) / 2 << endl;\n    return 0;\n}", "language": "c++", "problem_description": "CodeNet problem p27163", "test_cases": "", "libraries": ["iostream"], "difficulty": "Medium", "tags": ["codenet", "c++"], "patterns": ["simple_codenet_135814_func_main"], "timestamp": 1754139203.4438322}