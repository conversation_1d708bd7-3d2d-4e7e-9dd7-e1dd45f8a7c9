{"sample_id": "simple_codenet_555660", "dataset_name": "massive_codenet", "problem_id": "p111133", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p111133", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_555660_func_solve"], "timestamp": 1754148013.906751}