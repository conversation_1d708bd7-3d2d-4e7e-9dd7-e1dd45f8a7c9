{"sample_id": "simple_codenet_074859", "dataset_name": "simple_codenet", "problem_id": "p14972", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14972", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074859_func_solve"], "timestamp": 1754139063.9781296}