{"sample_id": "simple_codenet_053508", "dataset_name": "massive_codenet", "problem_id": "p10702", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p10702", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_053508_func_solve"], "timestamp": 1754147982.689214}