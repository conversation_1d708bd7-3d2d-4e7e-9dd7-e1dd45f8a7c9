{"sample_id": "simple_codenet_176482", "dataset_name": "simple_codenet", "problem_id": "p35297", "code": "#include <iostream>\nusing namespace std;\nint main() {\n    int n; cin >> n;\n    cout << n * (n + 1) / 2 << endl;\n    return 0;\n}", "language": "c++", "problem_description": "CodeNet problem p35297", "test_cases": "", "libraries": ["iostream"], "difficulty": "Medium", "tags": ["codenet", "c++"], "patterns": [], "timestamp": 1754139286.4458582}