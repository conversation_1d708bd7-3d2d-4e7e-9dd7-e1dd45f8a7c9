{"sample_id": "simple_codenet_140106", "dataset_name": "simple_codenet", "problem_id": "p28022", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p28022", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_140106_func_solve"], "timestamp": 1754139213.145177}