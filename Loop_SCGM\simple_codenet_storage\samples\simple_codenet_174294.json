{"sample_id": "simple_codenet_174294", "dataset_name": "simple_codenet", "problem_id": "p34859", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p34859", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_174294_func_solve"], "timestamp": 1754139282.9696245}