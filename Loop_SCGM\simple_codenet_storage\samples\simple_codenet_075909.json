{"sample_id": "simple_codenet_075909", "dataset_name": "simple_codenet", "problem_id": "p15182", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p15182", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_075909_func_solve"], "timestamp": 1754139066.273786}