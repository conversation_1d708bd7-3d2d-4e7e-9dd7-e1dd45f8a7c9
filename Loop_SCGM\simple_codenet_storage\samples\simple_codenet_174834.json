{"sample_id": "simple_codenet_174834", "dataset_name": "simple_codenet", "problem_id": "p34967", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p34967", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_174834_func_solve"], "timestamp": 1754139283.7912796}