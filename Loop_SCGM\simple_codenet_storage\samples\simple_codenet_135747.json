{"sample_id": "simple_codenet_135747", "dataset_name": "simple_codenet", "problem_id": "p27150", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27150", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135747_func_solve"], "timestamp": 1754139203.3347373}