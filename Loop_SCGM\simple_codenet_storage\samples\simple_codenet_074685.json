{"sample_id": "simple_codenet_074685", "dataset_name": "simple_codenet", "problem_id": "p14938", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14938", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074685_func_solve"], "timestamp": 1754139063.6425698}