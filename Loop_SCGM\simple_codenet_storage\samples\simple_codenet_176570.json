{"sample_id": "simple_codenet_176570", "dataset_name": "simple_codenet", "problem_id": "p35315", "code": "import java.util.*;\npublic class Solution {\n    public static void main(String[] args) {\n        Scanner sc = new Scanner(System.in);\n        int n = sc.nextInt();\n        System.out.println(n * (n + 1) / 2);\n    }\n}", "language": "java", "problem_description": "CodeNet problem p35315", "test_cases": "", "libraries": ["util"], "difficulty": "Hard", "tags": ["codenet", "java"], "patterns": ["simple_codenet_176570_func_Scanner"], "timestamp": 1754139286.5995138}