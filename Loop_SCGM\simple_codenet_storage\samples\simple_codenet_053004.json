{"sample_id": "simple_codenet_053004", "dataset_name": "massive_codenet", "problem_id": "p10601", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p10601", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_053004_func_solve"], "timestamp": 1754147975.6883006}