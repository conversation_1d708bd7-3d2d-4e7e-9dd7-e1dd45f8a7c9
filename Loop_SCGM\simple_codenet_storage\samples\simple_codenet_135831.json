{"sample_id": "simple_codenet_135831", "dataset_name": "simple_codenet", "problem_id": "p27167", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27167", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135831_func_solve"], "timestamp": 1754139203.4816318}