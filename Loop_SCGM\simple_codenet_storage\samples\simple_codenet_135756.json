{"sample_id": "simple_codenet_135756", "dataset_name": "simple_codenet", "problem_id": "p27152", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27152", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135756_func_solve"], "timestamp": 1754139203.3473566}