{"sample_id": "simple_codenet_074943", "dataset_name": "simple_codenet", "problem_id": "p14989", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14989", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074943_func_solve"], "timestamp": 1754139064.141507}