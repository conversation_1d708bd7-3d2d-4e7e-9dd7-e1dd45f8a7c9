{"sample_id": "simple_codenet_639615", "dataset_name": "simple_codenet", "problem_id": "p127924", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p127924", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_639615_func_solve"], "timestamp": 1754140156.998047}