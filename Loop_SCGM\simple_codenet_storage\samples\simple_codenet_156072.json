{"sample_id": "simple_codenet_156072", "dataset_name": "massive_codenet", "problem_id": "p31215", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p31215", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_156072_func_solve"], "timestamp": 1754148018.0186398}