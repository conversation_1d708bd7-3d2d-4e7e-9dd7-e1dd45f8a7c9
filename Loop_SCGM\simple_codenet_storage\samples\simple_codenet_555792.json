{"sample_id": "simple_codenet_555792", "dataset_name": "simple_codenet", "problem_id": "p111159", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p111159", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_555792_func_solve"], "timestamp": 1754140011.0396638}