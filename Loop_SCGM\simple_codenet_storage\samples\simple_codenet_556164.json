{"sample_id": "simple_codenet_556164", "dataset_name": "massive_codenet", "problem_id": "p111233", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p111233", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_556164_func_solve"], "timestamp": 1754148022.2649803}