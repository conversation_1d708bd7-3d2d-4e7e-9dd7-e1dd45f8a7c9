{"sample_id": "simple_codenet_399387", "dataset_name": "simple_codenet", "problem_id": "p79878", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p79878", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_399387_func_solve"], "timestamp": 1754139678.0965376}