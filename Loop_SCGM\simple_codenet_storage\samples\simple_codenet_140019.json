{"sample_id": "simple_codenet_140019", "dataset_name": "simple_codenet", "problem_id": "p28004", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p28004", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_140019_func_solve"], "timestamp": 1754139212.934388}