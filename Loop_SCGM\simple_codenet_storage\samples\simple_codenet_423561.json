{"sample_id": "simple_codenet_423561", "dataset_name": "simple_codenet", "problem_id": "p84713", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p84713", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_423561_func_solve"], "timestamp": 1754139744.9818478}