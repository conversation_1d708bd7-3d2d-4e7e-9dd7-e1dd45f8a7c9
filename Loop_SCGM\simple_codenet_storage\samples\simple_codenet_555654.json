{"sample_id": "simple_codenet_555654", "dataset_name": "simple_codenet", "problem_id": "p111131", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p111131", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_555654_func_solve"], "timestamp": 1754140010.7654076}