{"sample_id": "simple_codenet_174528", "dataset_name": "simple_codenet", "problem_id": "p34906", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p34906", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_174528_func_solve"], "timestamp": 1754139283.3425736}