{"sample_id": "simple_codenet_075345", "dataset_name": "simple_codenet", "problem_id": "p15070", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p15070", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_075345_func_solve"], "timestamp": 1754139065.1834207}