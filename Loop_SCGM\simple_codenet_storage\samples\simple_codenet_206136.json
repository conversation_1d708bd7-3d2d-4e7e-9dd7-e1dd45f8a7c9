{"sample_id": "simple_codenet_206136", "dataset_name": "massive_codenet", "problem_id": "p41228", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p41228", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_206136_func_solve"], "timestamp": 1754148019.3545206}