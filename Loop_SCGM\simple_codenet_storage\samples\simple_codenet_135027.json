{"sample_id": "simple_codenet_135027", "dataset_name": "simple_codenet", "problem_id": "p27006", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27006", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135027_func_solve"], "timestamp": 1754139201.6133175}