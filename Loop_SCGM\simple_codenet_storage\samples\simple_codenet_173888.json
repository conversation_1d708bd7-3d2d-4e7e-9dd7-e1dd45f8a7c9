{"sample_id": "simple_codenet_173888", "dataset_name": "simple_codenet", "problem_id": "p34778", "code": "import java.util.*;\npublic class Solution {\n    public static void main(String[] args) {\n        Scanner sc = new Scanner(System.in);\n        int n = sc.nextInt();\n        System.out.println(n * (n + 1) / 2);\n    }\n}", "language": "java", "problem_description": "CodeNet problem p34778", "test_cases": "", "libraries": ["util"], "difficulty": "Hard", "tags": ["codenet", "java"], "patterns": ["simple_codenet_173888_method_main", "simple_codenet_173888_method_Scanner", "simple_codenet_173888_class_Solution"], "timestamp": 1754139282.3288884}