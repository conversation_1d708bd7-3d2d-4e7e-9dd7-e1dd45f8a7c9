{"sample_id": "simple_codenet_135039", "dataset_name": "simple_codenet", "problem_id": "p27008", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27008", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135039_func_solve"], "timestamp": 1754139201.659718}