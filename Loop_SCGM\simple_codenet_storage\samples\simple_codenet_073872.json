{"sample_id": "simple_codenet_073872", "dataset_name": "simple_codenet", "problem_id": "p14775", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14775", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_073872_func_solve"], "timestamp": 1754139062.0129488}