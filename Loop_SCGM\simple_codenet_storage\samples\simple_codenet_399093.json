{"sample_id": "simple_codenet_399093", "dataset_name": "simple_codenet", "problem_id": "p79819", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p79819", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_399093_func_solve"], "timestamp": 1754139677.518372}