{"sample_id": "simple_codenet_423249", "dataset_name": "simple_codenet", "problem_id": "p84650", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p84650", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_423249_func_solve"], "timestamp": 1754139744.2351964}