{"sample_id": "simple_codenet_352368", "dataset_name": "simple_codenet", "problem_id": "p70474", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p70474", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_352368_func_solve"], "timestamp": 1754139577.620268}