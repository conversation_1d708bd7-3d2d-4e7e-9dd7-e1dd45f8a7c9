{"sample_id": "simple_codenet_194073", "dataset_name": "simple_codenet", "problem_id": "p38815", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p38815", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_194073_func_solve"], "timestamp": 1754139318.2330778}