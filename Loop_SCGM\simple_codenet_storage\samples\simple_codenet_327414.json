{"sample_id": "simple_codenet_327414", "dataset_name": "simple_codenet", "problem_id": "p65483", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p65483", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_327414_func_solve"], "timestamp": 1754139536.4010658}