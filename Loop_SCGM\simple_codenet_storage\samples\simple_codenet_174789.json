{"sample_id": "simple_codenet_174789", "dataset_name": "simple_codenet", "problem_id": "p34958", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p34958", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_174789_func_solve"], "timestamp": 1754139283.719705}