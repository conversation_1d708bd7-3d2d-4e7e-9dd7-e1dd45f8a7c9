{"sample_id": "simple_codenet_135804", "dataset_name": "simple_codenet", "problem_id": "p27161", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27161", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135804_func_solve"], "timestamp": 1754139203.4246972}