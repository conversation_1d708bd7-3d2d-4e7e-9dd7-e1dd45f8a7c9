{"sample_id": "simple_codenet_674967", "dataset_name": "simple_codenet", "problem_id": "p134994", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p134994", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_674967_func_solve"], "timestamp": 1754140222.0328856}