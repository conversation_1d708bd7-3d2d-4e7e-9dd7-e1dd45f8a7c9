{"sample_id": "simple_codenet_205916", "dataset_name": "massive_codenet", "problem_id": "p41184", "code": "function solve(n) {\n    return Array.from({length: n}, (_, i) => i).reduce((a, b) => a + b, 0);\n}", "language": "javascript", "problem_description": "CodeNet problem p41184", "test_cases": "", "libraries": [], "difficulty": "Hard", "tags": ["codenet", "javascript"], "patterns": [], "timestamp": 1754148016.5764184}