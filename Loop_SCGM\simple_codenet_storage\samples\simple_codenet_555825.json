{"sample_id": "simple_codenet_555825", "dataset_name": "simple_codenet", "problem_id": "p111166", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p111166", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_555825_func_solve"], "timestamp": 1754140011.0938566}