{"sample_id": "simple_codenet_555690", "dataset_name": "massive_codenet", "problem_id": "p111139", "code": "import java.util.*;\npublic class Solution {\n    public static void main(String[] args) {\n        Scanner sc = new Scanner(System.in);\n        int n = sc.nextInt();\n        System.out.println(n * (n + 1) / 2);\n    }\n}", "language": "java", "problem_description": "CodeNet problem p111139", "test_cases": "", "libraries": ["util"], "difficulty": "Easy", "tags": ["codenet", "java"], "patterns": ["simple_codenet_555690_func_Scanner"], "timestamp": 1754148014.468729}