{"sample_id": "simple_codenet_053317", "dataset_name": "massive_codenet", "problem_id": "p10664", "code": "package main\nimport (\n    \"fmt\"\n    \"sort\"\n)\nfunc main() {\n    arr := []int{3, 1, 4, 1, 5}\n    sort.Ints(arr)\n    fmt.Println(arr)\n}", "language": "go", "problem_description": "CodeNet problem p10664", "test_cases": "", "libraries": [], "difficulty": "Medium", "tags": ["codenet", "go"], "patterns": [], "timestamp": 1754147979.780115}