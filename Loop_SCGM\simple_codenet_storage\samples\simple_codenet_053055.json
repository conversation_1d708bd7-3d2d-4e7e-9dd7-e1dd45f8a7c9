{"sample_id": "simple_codenet_053055", "dataset_name": "simple_codenet", "problem_id": "p10612", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p10612", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_053055_func_solve"], "timestamp": 1754139018.4244888}