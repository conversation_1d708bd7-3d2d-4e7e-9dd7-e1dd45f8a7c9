{"sample_id": "simple_codenet_423540", "dataset_name": "simple_codenet", "problem_id": "p84709", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p84709", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_423540_func_solve"], "timestamp": 1754139744.9304216}