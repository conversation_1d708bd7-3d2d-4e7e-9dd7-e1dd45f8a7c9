{"sample_id": "simple_codenet_135162", "dataset_name": "simple_codenet", "problem_id": "p27033", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27033", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135162_func_solve"], "timestamp": 1754139201.9613566}