{"sample_id": "simple_codenet_399117", "dataset_name": "simple_codenet", "problem_id": "p79824", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p79824", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_399117_func_solve"], "timestamp": 1754139677.576938}