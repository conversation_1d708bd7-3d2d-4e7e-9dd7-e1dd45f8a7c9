{"sample_id": "simple_codenet_073851", "dataset_name": "simple_codenet", "problem_id": "p14771", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14771", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_073851_func_solve"], "timestamp": 1754139061.9683416}