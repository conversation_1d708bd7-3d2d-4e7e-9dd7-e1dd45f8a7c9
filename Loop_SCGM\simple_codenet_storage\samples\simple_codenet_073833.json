{"sample_id": "simple_codenet_073833", "dataset_name": "simple_codenet", "problem_id": "p14767", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14767", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_073833_func_solve"], "timestamp": 1754139061.930654}