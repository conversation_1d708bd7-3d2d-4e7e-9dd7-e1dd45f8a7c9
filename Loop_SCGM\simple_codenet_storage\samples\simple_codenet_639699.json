{"sample_id": "simple_codenet_639699", "dataset_name": "simple_codenet", "problem_id": "p127940", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p127940", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_639699_func_solve"], "timestamp": 1754140157.1677637}