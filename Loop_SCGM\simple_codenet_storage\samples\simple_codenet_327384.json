{"sample_id": "simple_codenet_327384", "dataset_name": "simple_codenet", "problem_id": "p65477", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p65477", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_327384_func_solve"], "timestamp": 1754139536.3117473}