{"sample_id": "simple_codenet_135576", "dataset_name": "simple_codenet", "problem_id": "p27116", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27116", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135576_func_solve"], "timestamp": 1754139202.9518812}