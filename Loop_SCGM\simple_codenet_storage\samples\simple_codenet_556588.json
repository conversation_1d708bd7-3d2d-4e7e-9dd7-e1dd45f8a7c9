{"sample_id": "simple_codenet_556588", "dataset_name": "massive_codenet", "problem_id": "p111318", "code": "function solve(n) {\n    return Array.from({length: n}, (_, i) => i).reduce((a, b) => a + b, 0);\n}", "language": "javascript", "problem_description": "CodeNet problem p111318", "test_cases": "", "libraries": [], "difficulty": "Medium", "tags": ["codenet", "javascript"], "patterns": [], "timestamp": 1754148030.7324114}