{"sample_id": "simple_codenet_174657", "dataset_name": "simple_codenet", "problem_id": "p34932", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p34932", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_174657_func_solve"], "timestamp": 1754139283.5279498}