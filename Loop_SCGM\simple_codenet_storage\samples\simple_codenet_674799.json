{"sample_id": "simple_codenet_674799", "dataset_name": "simple_codenet", "problem_id": "p134960", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p134960", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_674799_func_solve"], "timestamp": 1754140221.660844}