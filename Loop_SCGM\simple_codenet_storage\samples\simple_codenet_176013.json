{"sample_id": "simple_codenet_176013", "dataset_name": "simple_codenet", "problem_id": "p35203", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35203", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_176013_func_solve"], "timestamp": 1754139285.6356018}