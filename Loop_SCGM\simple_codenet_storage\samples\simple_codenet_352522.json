{"sample_id": "simple_codenet_352522", "dataset_name": "massive_codenet", "problem_id": "p70505", "code": "import java.util.*;\npublic class Solution {\n    public static void main(String[] args) {\n        Scanner sc = new Scanner(System.in);\n        int n = sc.nextInt();\n        System.out.println(n * (n + 1) / 2);\n    }\n}", "language": "java", "problem_description": "CodeNet problem p70505", "test_cases": "", "libraries": ["util"], "difficulty": "Medium", "tags": ["codenet", "java"], "patterns": ["simple_codenet_352522_func_Scanner"], "timestamp": 1754147967.118622}