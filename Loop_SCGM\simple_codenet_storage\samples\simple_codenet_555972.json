{"sample_id": "simple_codenet_555972", "dataset_name": "simple_codenet", "problem_id": "p111195", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p111195", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_555972_func_solve"], "timestamp": 1754140011.357274}