{"sample_id": "simple_codenet_193904", "dataset_name": "simple_codenet", "problem_id": "p38781", "code": "import java.util.*;\npublic class Solution {\n    public static void main(String[] args) {\n        Scanner sc = new Scanner(System.in);\n        int n = sc.nextInt();\n        System.out.println(n * (n + 1) / 2);\n    }\n}", "language": "java", "problem_description": "CodeNet problem p38781", "test_cases": "", "libraries": ["util"], "difficulty": "Hard", "tags": ["codenet", "java"], "patterns": ["simple_codenet_193904_method_main", "simple_codenet_193904_method_Scanner", "simple_codenet_193904_class_Solution"], "timestamp": 1754139317.9803636}