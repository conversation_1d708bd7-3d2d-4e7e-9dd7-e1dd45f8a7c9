{"sample_id": "simple_codenet_135024", "dataset_name": "simple_codenet", "problem_id": "p27005", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27005", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135024_func_solve"], "timestamp": 1754139201.606268}