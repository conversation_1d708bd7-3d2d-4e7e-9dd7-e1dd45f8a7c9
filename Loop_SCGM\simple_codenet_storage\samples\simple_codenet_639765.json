{"sample_id": "simple_codenet_639765", "dataset_name": "simple_codenet", "problem_id": "p127954", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p127954", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_639765_func_solve"], "timestamp": 1754140157.3188891}