{"sample_id": "simple_codenet_135057", "dataset_name": "simple_codenet", "problem_id": "p27012", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27012", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135057_func_solve"], "timestamp": 1754139201.6946752}