{"sample_id": "simple_codenet_075357", "dataset_name": "simple_codenet", "problem_id": "p15072", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p15072", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_075357_func_solve"], "timestamp": 1754139065.1986437}