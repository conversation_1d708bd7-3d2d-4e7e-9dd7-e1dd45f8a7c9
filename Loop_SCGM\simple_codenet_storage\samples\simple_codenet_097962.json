{"sample_id": "simple_codenet_097962", "dataset_name": "simple_codenet", "problem_id": "p19593", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p19593", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_097962_func_solve"], "timestamp": 1754139116.2866304}