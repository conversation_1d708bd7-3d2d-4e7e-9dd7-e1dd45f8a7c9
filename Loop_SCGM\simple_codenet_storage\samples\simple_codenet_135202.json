{"sample_id": "simple_codenet_135202", "dataset_name": "simple_codenet", "problem_id": "p27041", "code": "#include <iostream>\nusing namespace std;\nint main() {\n    int n; cin >> n;\n    cout << n * (n + 1) / 2 << endl;\n    return 0;\n}", "language": "c++", "problem_description": "CodeNet problem p27041", "test_cases": "", "libraries": ["iostream"], "difficulty": "Medium", "tags": ["codenet", "c++"], "patterns": ["simple_codenet_135202_func_main"], "timestamp": 1754139202.0452974}