{"sample_id": "simple_codenet_556445", "dataset_name": "massive_codenet", "problem_id": "p111290", "code": "#include <vector>\n#include <algorithm>\nusing namespace std;\nint main() {\n    vector<int> v = {3, 1, 4, 1, 5};\n    sort(v.begin(), v.end());\n    return 0;\n}", "language": "c++", "problem_description": "CodeNet problem p111290", "test_cases": "", "libraries": ["vector", "algorithm"], "difficulty": "Hard", "tags": ["codenet", "c++"], "patterns": [], "timestamp": 1754148028.5428154}