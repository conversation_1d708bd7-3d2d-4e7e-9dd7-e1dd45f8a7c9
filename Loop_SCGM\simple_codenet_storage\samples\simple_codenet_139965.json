{"sample_id": "simple_codenet_139965", "dataset_name": "simple_codenet", "problem_id": "p27994", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27994", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_139965_func_solve"], "timestamp": 1754139212.8244011}