{"sample_id": "simple_codenet_194388", "dataset_name": "simple_codenet", "problem_id": "p38878", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p38878", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_194388_func_solve"], "timestamp": 1754139318.8228476}