{"sample_id": "simple_codenet_399078", "dataset_name": "simple_codenet", "problem_id": "p79816", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p79816", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_399078_func_solve"], "timestamp": 1754139677.4945164}