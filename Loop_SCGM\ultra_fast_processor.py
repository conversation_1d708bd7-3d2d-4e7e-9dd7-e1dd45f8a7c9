#!/usr/bin/env python3
"""
🚀 ULTRA-FAST 30-MINUTE PROCESSOR
Processes massive datasets in 30 minutes using maximum parallelization
Target: 6M+ samples from The Stack + StarCoder in 30 minutes
"""

import os
import sys
import json
import time
import sqlite3
import hashlib
import zlib
import multiprocessing
import threading
from pathlib import Path
from typing import Dict, List, Any, Iterator
from concurrent.futures import ThreadPoolExecutor, as_completed

# HuggingFace authentication
from datasets import load_dataset

HF_TOKEN = "*************************************"

class UltraFastProcessor:
    """🚀 Ultra-fast 30-minute massive dataset processor"""
    
    def __init__(self):
        self.storage_root = Path("Loop_SCGM/simple_codenet_storage")
        self.storage_root.mkdir(parents=True, exist_ok=True)

        # MAXIMUM PERFORMANCE CONFIGURATION
        self.max_workers = min(32, multiprocessing.cpu_count() * 2)  # Maximum threads
        self.batch_size = 500_000  # MASSIVE batches

        # Use existing database
        self.db_path = self.storage_root / "scgm_code_intelligence.db"
        self.init_database()
        
        # 30-minute dataset configuration
        self.datasets = {
            'bigcode/the-stack': {'time_limit': 25, 'target_samples': 5_000_000},  # 5M in 25min
            'bigcode/starcoderbase': {'time_limit': 5, 'target_samples': 1_000_000},  # 1M in 5min
        }
        
        # Statistics
        self.stats = {
            'samples_processed': 0,
            'patterns_extracted': 0,
            'start_time': 0
        }
        self.stats_lock = threading.Lock()
        
        print(f"🚀 ULTRA-FAST PROCESSOR INITIALIZED")
        print(f"   💪 Max workers: {self.max_workers}")
        print(f"   📦 Batch size: {self.batch_size:,}")
        print(f"   ⏱️ Target: 30 minutes total")
    
    def init_database(self):
        """Initialize ultra-fast database"""
        conn = sqlite3.connect(self.db_path, timeout=60.0)
        conn.execute('PRAGMA journal_mode=WAL')
        conn.execute('PRAGMA synchronous=OFF')  # Maximum speed
        conn.execute('PRAGMA cache_size=100000')
        conn.execute('PRAGMA temp_store=MEMORY')
        cursor = conn.cursor()
        
        # Simple table for maximum speed
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS code_patterns (
                id INTEGER PRIMARY KEY,
                code_hash TEXT,
                language TEXT,
                pattern_type TEXT,
                source TEXT,
                timestamp REAL
            )
        ''')
        
        conn.commit()
        conn.close()
        print("✅ Ultra-fast database initialized")
    
    def ultra_fast_pattern_extract(self, code: str, language: str) -> List[str]:
        """Ultra-fast pattern extraction (5ms max)"""
        patterns = []
        
        try:
            # Skip large files immediately
            if len(code) > 5000:
                return ['large_file']
            
            code_lower = code.lower()
            
            # Super-fast pattern detection
            if any(kw in code for kw in ['def ', 'function ', 'func ', 'public ', 'void ']):
                patterns.append('function')
            
            if any(kw in code for kw in ['class ', 'struct ', 'interface ']):
                patterns.append('class')
            
            if any(kw in code_lower for kw in ['for', 'while']):
                patterns.append('loop')
            
            if any(kw in code_lower for kw in ['if', 'else']):
                patterns.append('conditional')
            
            return patterns if patterns else ['code_snippet']
            
        except:
            return ['unknown']
    
    def stream_massive_dataset(self, dataset_name: str, time_limit: int, target_samples: int) -> Iterator[Dict[str, Any]]:
        """Stream massive dataset at maximum speed"""
        start_time = time.time()
        samples_yielded = 0
        
        print(f"🔥 STREAMING {dataset_name} - TARGET: {target_samples:,} samples in {time_limit}min")
        
        try:
            if dataset_name == 'bigcode/the-stack':
                dataset = load_dataset("bigcode/the-stack", streaming=True, split="train", token=HF_TOKEN)
                
                for sample in dataset:
                    if (time.time() - start_time > time_limit * 60 or 
                        samples_yielded >= target_samples):
                        break
                    
                    content = sample.get('content', '')
                    if 50 < len(content) <= 20_000:  # Skip tiny/huge files
                        yield {
                            'code': content,
                            'language': sample.get('ext', 'unknown'),
                            'source': 'the_stack',
                            'sample_id': f"stack_{samples_yielded:08d}"
                        }
                        samples_yielded += 1
                        
                        # Ultra-fast progress reporting
                        if samples_yielded % 50_000 == 0:
                            elapsed = time.time() - start_time
                            rate = samples_yielded / elapsed if elapsed > 0 else 0
                            print(f"   🔥 {samples_yielded:,} samples | {rate:.0f}/sec")
            
            elif dataset_name == 'bigcode/starcoderbase':
                # Try multiple StarCoder datasets for maximum samples
                starcoder_datasets = ["bigcode/starcoderdata", "bigcode/starcoder"]
                
                for dataset_id in starcoder_datasets:
                    try:
                        dataset = load_dataset(dataset_id, streaming=True, split="train", token=HF_TOKEN)
                        
                        for sample in dataset:
                            if (time.time() - start_time > time_limit * 60 or 
                                samples_yielded >= target_samples):
                                break
                            
                            content = sample.get('content', '') or sample.get('text', '')
                            if 50 < len(content) <= 20_000:
                                yield {
                                    'code': content,
                                    'language': sample.get('programming_language', 'unknown'),
                                    'source': 'starcoderbase',
                                    'sample_id': f"starcoder_{samples_yielded:08d}"
                                }
                                samples_yielded += 1
                                
                                if samples_yielded % 25_000 == 0:
                                    elapsed = time.time() - start_time
                                    rate = samples_yielded / elapsed if elapsed > 0 else 0
                                    print(f"   🔥 {samples_yielded:,} samples | {rate:.0f}/sec")
                        
                        if samples_yielded >= target_samples:
                            break
                            
                    except Exception as e:
                        print(f"   ⚠️ {dataset_id}: {str(e)[:50]}...")
                        continue
        
        except Exception as e:
            print(f"❌ Error streaming {dataset_name}: {e}")
        
        elapsed = time.time() - start_time
        print(f"✅ {dataset_name}: {samples_yielded:,} samples in {elapsed:.1f}s")
    
    def ultra_fast_batch_process(self, batch: List[Dict[str, Any]]) -> Dict[str, int]:
        """Ultra-fast batch processing"""
        batch_stats = {'processed': 0, 'patterns': 0}
        
        # Use separate database connection for each thread
        conn = sqlite3.connect(self.db_path, timeout=60.0, check_same_thread=False)
        conn.execute('PRAGMA synchronous=OFF')
        cursor = conn.cursor()
        
        try:
            # Prepare bulk insert data
            insert_data = []
            
            for sample in batch:
                # Ultra-fast pattern extraction
                patterns = self.ultra_fast_pattern_extract(sample['code'], sample['language'])
                
                # Create hash for deduplication
                code_hash = hashlib.md5(sample['code'][:1000].encode()).hexdigest()
                
                # Prepare data for bulk insert
                for pattern in patterns:
                    insert_data.append((
                        code_hash,
                        sample['language'],
                        pattern,
                        sample['source'],
                        time.time()
                    ))
                    batch_stats['patterns'] += 1
                
                batch_stats['processed'] += 1
            
            # Bulk insert for maximum speed
            cursor.executemany('''
                INSERT OR IGNORE INTO code_patterns 
                (code_hash, language, pattern_type, source, timestamp)
                VALUES (?, ?, ?, ?, ?)
            ''', insert_data)
            
            conn.commit()
        
        except Exception as e:
            print(f"⚠️ Batch error: {e}")
        
        finally:
            conn.close()
        
        return batch_stats
    
    def update_stats(self, batch_stats: Dict[str, int]):
        """Thread-safe stats update"""
        with self.stats_lock:
            self.stats['samples_processed'] += batch_stats['processed']
            self.stats['patterns_extracted'] += batch_stats['patterns']
    
    def process_ultra_fast(self) -> bool:
        """🚀 ULTRA-FAST 30-MINUTE PROCESSING"""
        print("🚀 STARTING ULTRA-FAST 30-MINUTE PROCESSING")
        print("🎯 TARGET: 6M+ samples from The Stack + StarCoder")
        
        self.stats['start_time'] = time.time()
        
        try:
            for dataset_name, config in self.datasets.items():
                print(f"\n🔥 PROCESSING {dataset_name}")
                
                # Use maximum parallelization
                with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                    futures = []
                    batch = []
                    batch_count = 0
                    
                    for sample in self.stream_massive_dataset(
                        dataset_name, 
                        config['time_limit'], 
                        config['target_samples']
                    ):
                        batch.append(sample)
                        
                        if len(batch) >= self.batch_size:
                            # Submit massive batch for parallel processing
                            future = executor.submit(self.ultra_fast_batch_process, batch.copy())
                            futures.append(future)
                            batch = []
                            batch_count += 1
                            
                            # Collect completed futures
                            if len(futures) >= self.max_workers:
                                for future in as_completed(futures[:self.max_workers//2]):
                                    try:
                                        batch_stats = future.result(timeout=30)
                                        self.update_stats(batch_stats)
                                    except Exception as e:
                                        print(f"⚠️ Future error: {e}")
                                
                                futures = futures[self.max_workers//2:]
                            
                            # Progress reporting
                            if batch_count % 5 == 0:
                                elapsed = time.time() - self.stats['start_time']
                                rate = self.stats['samples_processed'] / elapsed if elapsed > 0 else 0
                                print(f"   📊 {self.stats['samples_processed']:,} samples | "
                                      f"{self.stats['patterns_extracted']:,} patterns | "
                                      f"{rate:.0f} samples/sec | {elapsed/60:.1f}min")
                    
                    # Process remaining batch
                    if batch:
                        future = executor.submit(self.ultra_fast_batch_process, batch)
                        futures.append(future)
                    
                    # Wait for all futures
                    for future in as_completed(futures):
                        try:
                            batch_stats = future.result(timeout=30)
                            self.update_stats(batch_stats)
                        except Exception as e:
                            print(f"⚠️ Final future error: {e}")
                
                print(f"✅ {dataset_name} complete")
            
            # Final results
            elapsed = time.time() - self.stats['start_time']
            rate = self.stats['samples_processed'] / elapsed if elapsed > 0 else 0
            
            print(f"\n🎉 ULTRA-FAST PROCESSING COMPLETE!")
            print(f"📊 FINAL RESULTS:")
            print(f"   ✅ Samples processed: {self.stats['samples_processed']:,}")
            print(f"   🧠 Patterns extracted: {self.stats['patterns_extracted']:,}")
            print(f"   ⏱️ Total time: {elapsed/60:.1f} minutes")
            print(f"   🚀 Average rate: {rate:.0f} samples/sec")
            print(f"   💾 Database: {self.db_path}")
            
            # Success criteria
            success = (
                self.stats['samples_processed'] >= 1_000_000 and  # 1M+ samples minimum
                elapsed <= 1800  # Under 30 minutes
            )
            
            print(f"   🎯 30-minute success: {'✅ YES' if success else '❌ NO'}")
            return success
            
        except Exception as e:
            print(f"❌ CRITICAL ERROR: {e}")
            return False

def main():
    """Main execution - Ultra-fast 30-minute processing"""
    print("🚀 ULTRA-FAST 30-MINUTE PROCESSOR")
    print("🎯 Processing The Stack + StarCoder in 30 minutes")
    
    processor = UltraFastProcessor()
    success = processor.process_ultra_fast()
    
    if success:
        print("\n🎉 30-MINUTE MISSION ACCOMPLISHED!")
    else:
        print("\n⚠️ PROCESSING COMPLETED - CHECK RESULTS")

if __name__ == "__main__":
    main()
