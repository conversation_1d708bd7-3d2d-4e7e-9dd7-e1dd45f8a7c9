{"sample_id": "simple_codenet_135592", "dataset_name": "simple_codenet", "problem_id": "p27119", "code": "#include <iostream>\nusing namespace std;\nint main() {\n    int n; cin >> n;\n    cout << n * (n + 1) / 2 << endl;\n    return 0;\n}", "language": "c++", "problem_description": "CodeNet problem p27119", "test_cases": "", "libraries": ["iostream"], "difficulty": "Medium", "tags": ["codenet", "c++"], "patterns": ["simple_codenet_135592_func_main"], "timestamp": 1754139202.9925976}