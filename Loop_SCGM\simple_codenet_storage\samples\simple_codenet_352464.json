{"sample_id": "simple_codenet_352464", "dataset_name": "massive_codenet", "problem_id": "p70493", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p70493", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_352464_func_solve"], "timestamp": 1754147966.393742}