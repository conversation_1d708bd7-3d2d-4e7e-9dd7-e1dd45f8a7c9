{"sample_id": "simple_codenet_053457", "dataset_name": "massive_codenet", "problem_id": "p10692", "code": "package main\nimport (\n    \"fmt\"\n    \"sort\"\n)\nfunc main() {\n    arr := []int{3, 1, 4, 1, 5}\n    sort.Ints(arr)\n    fmt.Println(arr)\n}", "language": "go", "problem_description": "CodeNet problem p10692", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "go"], "patterns": [], "timestamp": 1754147981.915413}