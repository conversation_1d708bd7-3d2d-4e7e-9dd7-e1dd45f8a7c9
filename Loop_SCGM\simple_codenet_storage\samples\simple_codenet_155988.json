{"sample_id": "simple_codenet_155988", "dataset_name": "massive_codenet", "problem_id": "p31198", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p31198", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_155988_func_solve"], "timestamp": 1754148016.9222493}