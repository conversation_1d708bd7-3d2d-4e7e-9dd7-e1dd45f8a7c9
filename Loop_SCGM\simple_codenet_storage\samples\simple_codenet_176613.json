{"sample_id": "simple_codenet_176613", "dataset_name": "simple_codenet", "problem_id": "p35323", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35323", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_176613_func_solve"], "timestamp": 1754139286.647435}