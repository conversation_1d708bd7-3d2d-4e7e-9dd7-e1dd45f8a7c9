{"sample_id": "simple_codenet_194061", "dataset_name": "simple_codenet", "problem_id": "p38813", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p38813", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_194061_func_solve"], "timestamp": 1754139318.216439}