{"sample_id": "simple_codenet_074049", "dataset_name": "simple_codenet", "problem_id": "p14810", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14810", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074049_func_solve"], "timestamp": 1754139062.3786354}