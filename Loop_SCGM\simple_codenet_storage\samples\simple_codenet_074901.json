{"sample_id": "simple_codenet_074901", "dataset_name": "simple_codenet", "problem_id": "p14981", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14981", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074901_func_solve"], "timestamp": 1754139064.0502982}