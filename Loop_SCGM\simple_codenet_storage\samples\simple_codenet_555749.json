{"sample_id": "simple_codenet_555749", "dataset_name": "massive_codenet", "problem_id": "p111150", "code": "package main\nimport (\n    \"fmt\"\n    \"sort\"\n)\nfunc main() {\n    arr := []int{3, 1, 4, 1, 5}\n    sort.Ints(arr)\n    fmt.Println(arr)\n}", "language": "go", "problem_description": "CodeNet problem p111150", "test_cases": "", "libraries": [], "difficulty": "Hard", "tags": ["codenet", "go"], "patterns": [], "timestamp": 1754148015.314654}