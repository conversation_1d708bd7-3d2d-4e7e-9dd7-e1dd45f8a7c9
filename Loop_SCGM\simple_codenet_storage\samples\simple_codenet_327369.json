{"sample_id": "simple_codenet_327369", "dataset_name": "simple_codenet", "problem_id": "p65474", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p65474", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_327369_func_solve"], "timestamp": 1754139536.2694702}