{"sample_id": "simple_codenet_053364", "dataset_name": "simple_codenet", "problem_id": "p10673", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p10673", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_053364_func_solve"], "timestamp": 1754139018.9021149}