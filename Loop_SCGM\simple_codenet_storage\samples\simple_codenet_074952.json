{"sample_id": "simple_codenet_074952", "dataset_name": "simple_codenet", "problem_id": "p14991", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14991", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074952_func_solve"], "timestamp": 1754139064.1555886}