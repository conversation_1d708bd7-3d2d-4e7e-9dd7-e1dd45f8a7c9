{"sample_id": "simple_codenet_176133", "dataset_name": "simple_codenet", "problem_id": "p35227", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35227", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_176133_func_solve"], "timestamp": 1754139285.835702}