{"sample_id": "simple_codenet_399213", "dataset_name": "simple_codenet", "problem_id": "p79843", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p79843", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_399213_func_solve"], "timestamp": 1754139677.755779}