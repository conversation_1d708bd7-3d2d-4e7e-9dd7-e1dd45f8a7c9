{"sample_id": "simple_codenet_135882", "dataset_name": "simple_codenet", "problem_id": "p27177", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27177", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135882_func_solve"], "timestamp": 1754139203.5928586}