{"sample_id": "simple_codenet_674817", "dataset_name": "simple_codenet", "problem_id": "p134964", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p134964", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_674817_func_solve"], "timestamp": 1754140221.7079277}