{"sample_id": "simple_codenet_327183", "dataset_name": "simple_codenet", "problem_id": "p65437", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p65437", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_327183_func_solve"], "timestamp": 1754139535.821189}