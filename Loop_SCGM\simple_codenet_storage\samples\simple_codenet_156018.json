{"sample_id": "simple_codenet_156018", "dataset_name": "simple_codenet", "problem_id": "p31204", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p31204", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_156018_func_solve"], "timestamp": 1754139250.2224865}