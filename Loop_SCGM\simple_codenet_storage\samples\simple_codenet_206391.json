{"sample_id": "simple_codenet_206391", "dataset_name": "massive_codenet", "problem_id": "p41279", "code": "#include <stdio.h>\n#include <stdlib.h>\nint compare(const void *a, const void *b) {\n    return (*(int*)a - *(int*)b);\n}\nint main() {\n    int arr[] = {3, 1, 4, 1, 5};\n    qsort(arr, 5, sizeof(int), compare);\n    return 0;\n}", "language": "c", "problem_description": "CodeNet problem p41279", "test_cases": "", "libraries": ["stdlib", "stdio"], "difficulty": "Easy", "tags": ["codenet", "c"], "patterns": ["simple_codenet_206391_func_compare"], "timestamp": 1754148026.0278044}