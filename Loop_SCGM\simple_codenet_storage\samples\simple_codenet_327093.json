{"sample_id": "simple_codenet_327093", "dataset_name": "simple_codenet", "problem_id": "p65419", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p65419", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_327093_func_solve"], "timestamp": 1754139535.6612837}