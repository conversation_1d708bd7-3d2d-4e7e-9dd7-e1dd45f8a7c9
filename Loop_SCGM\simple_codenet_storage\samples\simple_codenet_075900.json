{"sample_id": "simple_codenet_075900", "dataset_name": "simple_codenet", "problem_id": "p15181", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p15181", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_075900_func_solve"], "timestamp": 1754139066.2560668}