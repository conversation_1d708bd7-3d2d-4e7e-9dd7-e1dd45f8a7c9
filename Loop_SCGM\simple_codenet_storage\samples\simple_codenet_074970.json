{"sample_id": "simple_codenet_074970", "dataset_name": "simple_codenet", "problem_id": "p14995", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14995", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074970_func_solve"], "timestamp": 1754139064.1884437}