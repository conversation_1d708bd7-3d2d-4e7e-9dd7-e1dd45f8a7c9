{"sample_id": "simple_codenet_206556", "dataset_name": "massive_codenet", "problem_id": "p41312", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p41312", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_206556_func_solve"], "timestamp": 1754148028.7206764}