{"sample_id": "simple_codenet_074880", "dataset_name": "simple_codenet", "problem_id": "p14977", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14977", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074880_func_solve"], "timestamp": 1754139064.0188828}