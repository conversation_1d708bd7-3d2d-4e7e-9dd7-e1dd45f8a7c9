{"sample_id": "simple_codenet_135864", "dataset_name": "simple_codenet", "problem_id": "p27173", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27173", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135864_func_solve"], "timestamp": 1754139203.562449}