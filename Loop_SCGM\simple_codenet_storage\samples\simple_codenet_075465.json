{"sample_id": "simple_codenet_075465", "dataset_name": "simple_codenet", "problem_id": "p15094", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p15094", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_075465_func_solve"], "timestamp": 1754139065.3717422}