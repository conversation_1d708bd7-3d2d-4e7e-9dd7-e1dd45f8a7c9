{"sample_id": "simple_codenet_074916", "dataset_name": "simple_codenet", "problem_id": "p14984", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14984", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074916_func_solve"], "timestamp": 1754139064.0751593}