{"sample_id": "simple_codenet_140052", "dataset_name": "simple_codenet", "problem_id": "p28011", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p28011", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_140052_func_solve"], "timestamp": 1754139213.005884}