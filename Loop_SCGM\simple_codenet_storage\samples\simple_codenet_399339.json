{"sample_id": "simple_codenet_399339", "dataset_name": "simple_codenet", "problem_id": "p79868", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p79868", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_399339_func_solve"], "timestamp": 1754139677.987662}