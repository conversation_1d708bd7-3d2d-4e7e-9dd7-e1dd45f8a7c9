{"sample_id": "simple_codenet_399123", "dataset_name": "simple_codenet", "problem_id": "p79825", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p79825", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_399123_func_solve"], "timestamp": 1754139677.5870876}