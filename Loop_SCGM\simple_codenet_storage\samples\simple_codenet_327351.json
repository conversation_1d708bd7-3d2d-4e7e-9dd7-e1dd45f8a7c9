{"sample_id": "simple_codenet_327351", "dataset_name": "simple_codenet", "problem_id": "p65471", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p65471", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_327351_func_solve"], "timestamp": 1754139536.2226112}