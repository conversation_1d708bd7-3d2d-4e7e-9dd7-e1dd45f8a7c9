{"sample_id": "simple_codenet_135186", "dataset_name": "simple_codenet", "problem_id": "p27038", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27038", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135186_func_solve"], "timestamp": 1754139202.00656}