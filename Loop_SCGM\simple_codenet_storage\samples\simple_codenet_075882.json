{"sample_id": "simple_codenet_075882", "dataset_name": "simple_codenet", "problem_id": "p15177", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p15177", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_075882_func_solve"], "timestamp": 1754139066.2214139}