{"sample_id": "simple_codenet_639753", "dataset_name": "simple_codenet", "problem_id": "p127951", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p127951", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_639753_func_solve"], "timestamp": 1754140157.2953265}