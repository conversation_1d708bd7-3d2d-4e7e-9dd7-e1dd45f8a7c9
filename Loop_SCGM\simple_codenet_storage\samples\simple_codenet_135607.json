{"sample_id": "simple_codenet_135607", "dataset_name": "simple_codenet", "problem_id": "p27122", "code": "#include <vector>\n#include <algorithm>\nusing namespace std;\nint main() {\n    vector<int> v = {3, 1, 4, 1, 5};\n    sort(v.begin(), v.end());\n    return 0;\n}", "language": "c++", "problem_description": "CodeNet problem p27122", "test_cases": "", "libraries": ["algorithm", "vector"], "difficulty": "Medium", "tags": ["codenet", "c++"], "patterns": ["simple_codenet_135607_func_main"], "timestamp": 1754139203.02474}