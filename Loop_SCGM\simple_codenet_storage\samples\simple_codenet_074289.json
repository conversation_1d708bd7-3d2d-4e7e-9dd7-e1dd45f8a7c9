{"sample_id": "simple_codenet_074289", "dataset_name": "simple_codenet", "problem_id": "p14858", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14858", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074289_func_solve"], "timestamp": 1754139062.8355308}