{"sample_id": "simple_codenet_135093", "dataset_name": "simple_codenet", "problem_id": "p27019", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27019", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135093_func_solve"], "timestamp": 1754139201.783816}