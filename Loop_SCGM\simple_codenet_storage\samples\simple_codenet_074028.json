{"sample_id": "simple_codenet_074028", "dataset_name": "simple_codenet", "problem_id": "p14806", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14806", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074028_func_solve"], "timestamp": 1754139062.3300467}