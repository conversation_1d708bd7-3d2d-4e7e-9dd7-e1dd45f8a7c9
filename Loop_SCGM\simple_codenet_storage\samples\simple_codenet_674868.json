{"sample_id": "simple_codenet_674868", "dataset_name": "simple_codenet", "problem_id": "p134974", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p134974", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_674868_func_solve"], "timestamp": 1754140221.7948039}