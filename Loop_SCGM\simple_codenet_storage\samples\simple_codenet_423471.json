{"sample_id": "simple_codenet_423471", "dataset_name": "simple_codenet", "problem_id": "p84695", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p84695", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_423471_func_solve"], "timestamp": 1754139744.7562225}