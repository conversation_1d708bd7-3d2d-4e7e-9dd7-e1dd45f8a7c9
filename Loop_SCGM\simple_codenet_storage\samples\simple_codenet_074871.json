{"sample_id": "simple_codenet_074871", "dataset_name": "simple_codenet", "problem_id": "p14975", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14975", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074871_func_solve"], "timestamp": 1754139064.000215}