{"sample_id": "simple_codenet_075768", "dataset_name": "simple_codenet", "problem_id": "p15154", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p15154", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_075768_func_solve"], "timestamp": 1754139066.021966}