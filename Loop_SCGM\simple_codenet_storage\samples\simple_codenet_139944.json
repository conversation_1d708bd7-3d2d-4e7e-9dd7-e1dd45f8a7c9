{"sample_id": "simple_codenet_139944", "dataset_name": "simple_codenet", "problem_id": "p27989", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27989", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_139944_func_solve"], "timestamp": 1754139212.7728074}