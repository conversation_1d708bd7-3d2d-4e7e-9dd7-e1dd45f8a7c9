{"sample_id": "simple_codenet_053256", "dataset_name": "massive_codenet", "problem_id": "p10652", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p10652", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_053256_func_solve"], "timestamp": 1754147978.7745862}