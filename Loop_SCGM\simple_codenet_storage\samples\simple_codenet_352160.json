{"sample_id": "simple_codenet_352160", "dataset_name": "massive_codenet", "problem_id": "p70433", "code": "function solve(n) {\n    return Array.from({length: n}, (_, i) => i).reduce((a, b) => a + b, 0);\n}", "language": "javascript", "problem_description": "CodeNet problem p70433", "test_cases": "", "libraries": [], "difficulty": "Hard", "tags": ["codenet", "javascript"], "patterns": [], "timestamp": 1754147962.2705414}