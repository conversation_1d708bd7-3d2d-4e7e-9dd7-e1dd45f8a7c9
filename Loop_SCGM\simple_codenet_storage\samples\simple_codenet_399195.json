{"sample_id": "simple_codenet_399195", "dataset_name": "simple_codenet", "problem_id": "p79840", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p79840", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_399195_func_solve"], "timestamp": 1754139677.716849}