{"sample_id": "simple_codenet_206220", "dataset_name": "massive_codenet", "problem_id": "p41245", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p41245", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_206220_func_solve"], "timestamp": 1754148021.5410607}