{"sample_id": "simple_codenet_135693", "dataset_name": "simple_codenet", "problem_id": "p27139", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27139", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135693_func_solve"], "timestamp": 1754139203.2113237}