{"sample_id": "simple_codenet_327117", "dataset_name": "simple_codenet", "problem_id": "p65424", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p65424", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_327117_func_solve"], "timestamp": 1754139535.6944144}