{"sample_id": "simple_codenet_075951", "dataset_name": "simple_codenet", "problem_id": "p15191", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p15191", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_075951_func_solve"], "timestamp": 1754139066.3470333}