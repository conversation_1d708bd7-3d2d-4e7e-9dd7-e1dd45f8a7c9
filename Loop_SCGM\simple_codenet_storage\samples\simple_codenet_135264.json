{"sample_id": "simple_codenet_135264", "dataset_name": "simple_codenet", "problem_id": "p27053", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27053", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135264_func_solve"], "timestamp": 1754139202.1872518}