{"sample_id": "simple_codenet_206472", "dataset_name": "massive_codenet", "problem_id": "p41295", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p41295", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_206472_func_solve"], "timestamp": 1754148027.3235154}