{"sample_id": "simple_codenet_074461", "dataset_name": "simple_codenet", "problem_id": "p14893", "code": "#include <vector>\n#include <algorithm>\nusing namespace std;\nint main() {\n    vector<int> v = {3, 1, 4, 1, 5};\n    sort(v.begin(), v.end());\n    return 0;\n}", "language": "c++", "problem_description": "CodeNet problem p14893", "test_cases": "", "libraries": ["algorithm", "vector"], "difficulty": "Medium", "tags": ["codenet", "c++"], "patterns": [], "timestamp": 1754139063.2372553}