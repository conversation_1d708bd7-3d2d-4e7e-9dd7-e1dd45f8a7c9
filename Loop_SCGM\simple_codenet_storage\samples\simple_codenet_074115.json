{"sample_id": "simple_codenet_074115", "dataset_name": "simple_codenet", "problem_id": "p14824", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14824", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074115_func_solve"], "timestamp": 1754139062.4906542}