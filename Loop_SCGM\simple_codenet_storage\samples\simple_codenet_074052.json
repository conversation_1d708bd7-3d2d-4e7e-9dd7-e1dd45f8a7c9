{"sample_id": "simple_codenet_074052", "dataset_name": "simple_codenet", "problem_id": "p14811", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14811", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074052_func_solve"], "timestamp": 1754139062.3826408}