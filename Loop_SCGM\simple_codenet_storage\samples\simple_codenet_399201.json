{"sample_id": "simple_codenet_399201", "dataset_name": "simple_codenet", "problem_id": "p79841", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p79841", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_399201_func_solve"], "timestamp": 1754139677.7289448}