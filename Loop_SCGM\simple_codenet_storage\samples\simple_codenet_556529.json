{"sample_id": "simple_codenet_556529", "dataset_name": "massive_codenet", "problem_id": "p111306", "code": "#include <vector>\n#include <algorithm>\nusing namespace std;\nint main() {\n    vector<int> v = {3, 1, 4, 1, 5};\n    sort(v.begin(), v.end());\n    return 0;\n}", "language": "c++", "problem_description": "CodeNet problem p111306", "test_cases": "", "libraries": ["vector", "algorithm"], "difficulty": "Hard", "tags": ["codenet", "c++"], "patterns": [], "timestamp": 1754148029.839097}