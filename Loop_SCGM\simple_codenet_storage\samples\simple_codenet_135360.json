{"sample_id": "simple_codenet_135360", "dataset_name": "simple_codenet", "problem_id": "p27073", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27073", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135360_func_solve"], "timestamp": 1754139202.392294}