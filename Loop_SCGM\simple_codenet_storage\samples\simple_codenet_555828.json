{"sample_id": "simple_codenet_555828", "dataset_name": "massive_codenet", "problem_id": "p111166", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p111166", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_555828_func_solve"], "timestamp": 1754148016.3564095}