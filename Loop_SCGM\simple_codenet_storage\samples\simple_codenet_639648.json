{"sample_id": "simple_codenet_639648", "dataset_name": "simple_codenet", "problem_id": "p127930", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p127930", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_639648_func_solve"], "timestamp": 1754140157.0640926}