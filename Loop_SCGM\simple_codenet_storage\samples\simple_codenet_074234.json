{"sample_id": "simple_codenet_074234", "dataset_name": "simple_codenet", "problem_id": "p14847", "code": "import java.util.*;\npublic class Solution {\n    public static void main(String[] args) {\n        Scanner sc = new Scanner(System.in);\n        int n = sc.nextInt();\n        System.out.println(n * (n + 1) / 2);\n    }\n}", "language": "java", "problem_description": "CodeNet problem p14847", "test_cases": "", "libraries": ["util"], "difficulty": "Hard", "tags": ["codenet", "java"], "patterns": ["simple_codenet_074234_func_Scanner"], "timestamp": 1754139062.7078826}