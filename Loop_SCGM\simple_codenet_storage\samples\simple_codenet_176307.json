{"sample_id": "simple_codenet_176307", "dataset_name": "simple_codenet", "problem_id": "p35262", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35262", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_176307_func_solve"], "timestamp": 1754139286.1306026}