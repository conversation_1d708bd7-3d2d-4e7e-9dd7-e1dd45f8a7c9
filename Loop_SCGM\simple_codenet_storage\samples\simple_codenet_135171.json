{"sample_id": "simple_codenet_135171", "dataset_name": "simple_codenet", "problem_id": "p27035", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27035", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135171_func_solve"], "timestamp": 1754139201.976229}