{"sample_id": "simple_codenet_399234", "dataset_name": "simple_codenet", "problem_id": "p79847", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p79847", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_399234_func_solve"], "timestamp": 1754139677.8165765}