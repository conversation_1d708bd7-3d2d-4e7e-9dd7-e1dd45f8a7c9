{"sample_id": "simple_codenet_134872", "dataset_name": "simple_codenet", "problem_id": "p26975", "code": "#include <iostream>\nusing namespace std;\nint main() {\n    int n; cin >> n;\n    cout << n * (n + 1) / 2 << endl;\n    return 0;\n}", "language": "c++", "problem_description": "CodeNet problem p26975", "test_cases": "", "libraries": ["iostream"], "difficulty": "Medium", "tags": ["codenet", "c++"], "patterns": ["simple_codenet_134872_func_main"], "timestamp": 1754139201.257219}