{"sample_id": "simple_codenet_135642", "dataset_name": "simple_codenet", "problem_id": "p27129", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27129", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135642_func_solve"], "timestamp": 1754139203.1138852}