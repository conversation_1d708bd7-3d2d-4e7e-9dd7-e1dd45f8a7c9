{"sample_id": "simple_codenet_327186", "dataset_name": "simple_codenet", "problem_id": "p65438", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p65438", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_327186_func_solve"], "timestamp": 1754139535.8281827}