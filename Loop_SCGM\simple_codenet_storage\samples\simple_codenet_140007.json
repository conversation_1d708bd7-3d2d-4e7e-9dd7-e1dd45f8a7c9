{"sample_id": "simple_codenet_140007", "dataset_name": "simple_codenet", "problem_id": "p28002", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p28002", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_140007_func_solve"], "timestamp": 1754139212.9157758}