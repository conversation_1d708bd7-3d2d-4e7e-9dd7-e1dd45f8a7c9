{"sample_id": "simple_codenet_140088", "dataset_name": "simple_codenet", "problem_id": "p28018", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p28018", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_140088_func_solve"], "timestamp": 1754139213.1074588}