{"sample_id": "simple_codenet_206504", "dataset_name": "massive_codenet", "problem_id": "p41301", "code": "function solve(n) {\n    return Array.from({length: n}, (_, i) => i).reduce((a, b) => a + b, 0);\n}", "language": "javascript", "problem_description": "CodeNet problem p41301", "test_cases": "", "libraries": [], "difficulty": "Hard", "tags": ["codenet", "javascript"], "patterns": [], "timestamp": 1754148027.8298697}