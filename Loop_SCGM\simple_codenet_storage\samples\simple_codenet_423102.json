{"sample_id": "simple_codenet_423102", "dataset_name": "simple_codenet", "problem_id": "p84621", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p84621", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_423102_func_solve"], "timestamp": 1754139743.7243683}