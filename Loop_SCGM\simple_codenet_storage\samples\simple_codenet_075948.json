{"sample_id": "simple_codenet_075948", "dataset_name": "simple_codenet", "problem_id": "p15190", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p15190", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_075948_func_solve"], "timestamp": 1754139066.3253443}