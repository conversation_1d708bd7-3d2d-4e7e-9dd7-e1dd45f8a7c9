{"sample_id": "simple_codenet_555721", "dataset_name": "massive_codenet", "problem_id": "p111145", "code": "package main\nimport (\n    \"fmt\"\n    \"sort\"\n)\nfunc main() {\n    arr := []int{3, 1, 4, 1, 5}\n    sort.Ints(arr)\n    fmt.Println(arr)\n}", "language": "go", "problem_description": "CodeNet problem p111145", "test_cases": "", "libraries": [], "difficulty": "Medium", "tags": ["codenet", "go"], "patterns": [], "timestamp": 1754148014.864727}