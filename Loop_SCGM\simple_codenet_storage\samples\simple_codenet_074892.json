{"sample_id": "simple_codenet_074892", "dataset_name": "simple_codenet", "problem_id": "p14979", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14979", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074892_func_solve"], "timestamp": 1754139064.035121}