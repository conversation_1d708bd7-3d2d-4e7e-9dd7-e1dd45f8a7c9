{"sample_id": "simple_codenet_194361", "dataset_name": "simple_codenet", "problem_id": "p38873", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p38873", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_194361_func_solve"], "timestamp": 1754139318.7730744}