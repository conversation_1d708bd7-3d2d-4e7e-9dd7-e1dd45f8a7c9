{"sample_id": "simple_codenet_174885", "dataset_name": "simple_codenet", "problem_id": "p34978", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p34978", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_174885_func_solve"], "timestamp": 1754139283.8642085}