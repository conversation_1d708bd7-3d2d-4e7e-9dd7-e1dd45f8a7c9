{"sample_id": "simple_codenet_098043", "dataset_name": "simple_codenet", "problem_id": "p19609", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p19609", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_098043_func_solve"], "timestamp": 1754139116.4833922}