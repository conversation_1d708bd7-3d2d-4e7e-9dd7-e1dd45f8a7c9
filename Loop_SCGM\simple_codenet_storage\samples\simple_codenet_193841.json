{"sample_id": "simple_codenet_193841", "dataset_name": "simple_codenet", "problem_id": "p38769", "code": "import java.util.Arrays;\npublic class ArraySort {\n    public static void main(String[] args) {\n        int[] arr = {3, 1, 4, 1, 5};\n        Arrays.sort(arr);\n    }\n}", "language": "java", "problem_description": "CodeNet problem p38769", "test_cases": "", "libraries": ["<PERSON><PERSON><PERSON>"], "difficulty": "Hard", "tags": ["codenet", "java"], "patterns": ["simple_codenet_193841_method_main", "simple_codenet_193841_class_ArraySort"], "timestamp": 1754139317.8992772}