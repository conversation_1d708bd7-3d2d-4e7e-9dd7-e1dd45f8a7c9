{"sample_id": "simple_codenet_140055", "dataset_name": "simple_codenet", "problem_id": "p28012", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p28012", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_140055_func_solve"], "timestamp": 1754139213.0377882}