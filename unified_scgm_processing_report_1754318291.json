{"processing_completed": true, "total_datasets_processed": 7, "successful_datasets": 7, "total_files_processed": 46104, "total_patterns_extracted": 36310, "total_files_deleted": 46104, "total_processing_time": 129.07162976264954, "final_pattern_count": 1454102, "dataset_distribution": {"null": 1417792, "The_Stack": 35385, "GitHub": 600, "StackOverflow": 200, "CodeSearchNet": 75, "CodeContests": 50}, "type_distribution": {"function": 568615, "method": 409934, "class": 276737, "code_block": 174107, "conditional": 14254, "loop": 10205, "qa_pair": 200, "algorithm": 50}, "language_distribution": {"java": 680772, "c": 285788, "python": 271755, "abap": 35385, "javascript": 7543, "fortran": 7538, "r": 7538, "rust": 7535, "haskell": 7533, "php": 7533, "julia": 7529, "kotlin": 7526, "c#": 7521, "dart": 7521, "lua": 7519, "erlang": 7517, "f#": 7516, "scala": 7516, "perl": 7515, "clojure": 7510, "elixir": 7507, "ruby": 7507, "typescript": 7507, "pascal": 7504, "swift": 7500, "go": 7491, "assembly": 7489, "matlab": 7487}, "dataset_results": [{"source_name": "StackOverflow_850k", "files_processed": 22345, "patterns_extracted": 200, "processing_time": 0.36453700065612793, "success": true, "error_message": null, "files_deleted": 22345}, {"source_name": "HuggingFace_Transformers", "files_processed": 668, "patterns_extracted": 100, "processing_time": 0.18596982955932617, "success": true, "error_message": null, "files_deleted": 668}, {"source_name": "GitHub_Popular_Repos", "files_processed": 2567, "patterns_extracted": 500, "processing_time": 0.8554837703704834, "success": true, "error_message": null, "files_deleted": 2567}, {"source_name": "Papers_With_Code", "files_processed": 0, "patterns_extracted": 0, "processing_time": 0.0, "success": true, "error_message": null, "files_deleted": 0}, {"source_name": "The_Stack_Samples", "files_processed": 18214, "patterns_extracted": 35385, "processing_time": 127.34831809997559, "success": true, "error_message": null, "files_deleted": 18214}, {"source_name": "CodeSearchNet", "files_processed": 1443, "patterns_extracted": 75, "processing_time": 0.14916419982910156, "success": true, "error_message": null, "files_deleted": 1443}, {"source_name": "CodeContests", "files_processed": 867, "patterns_extracted": 50, "processing_time": 0.09842324256896973, "success": true, "error_message": null, "files_deleted": 867}]}