{"sample_id": "simple_codenet_176397", "dataset_name": "simple_codenet", "problem_id": "p35280", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35280", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_176397_func_solve"], "timestamp": 1754139286.2906601}