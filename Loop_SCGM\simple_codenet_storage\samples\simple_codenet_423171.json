{"sample_id": "simple_codenet_423171", "dataset_name": "simple_codenet", "problem_id": "p84635", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p84635", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_423171_func_solve"], "timestamp": 1754139743.9914253}