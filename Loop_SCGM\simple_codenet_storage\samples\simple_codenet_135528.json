{"sample_id": "simple_codenet_135528", "dataset_name": "simple_codenet", "problem_id": "p27106", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27106", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135528_func_solve"], "timestamp": 1754139202.8413372}