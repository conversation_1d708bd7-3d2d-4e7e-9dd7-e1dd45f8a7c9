{"sample_id": "simple_codenet_423201", "dataset_name": "simple_codenet", "problem_id": "p84641", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p84641", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_423201_func_solve"], "timestamp": 1754139744.0875275}