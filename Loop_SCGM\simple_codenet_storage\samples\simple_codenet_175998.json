{"sample_id": "simple_codenet_175998", "dataset_name": "simple_codenet", "problem_id": "p35200", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35200", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_175998_func_solve"], "timestamp": 1754139285.6079674}