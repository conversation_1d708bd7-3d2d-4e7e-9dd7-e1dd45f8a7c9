{"sample_id": "simple_codenet_206780", "dataset_name": "massive_codenet", "problem_id": "p41357", "code": "def binary_search(arr, target):\n    left, right = 0, len(arr) - 1\n    while left <= right:\n        mid = (left + right) // 2\n        if arr[mid] == target: return mid\n        elif arr[mid] < target: left = mid + 1\n        else: right = mid - 1\n    return -1", "language": "python", "problem_description": "CodeNet problem p41357", "test_cases": "", "libraries": [], "difficulty": "Hard", "tags": ["codenet", "python"], "patterns": ["simple_codenet_206780_func_binary_search", "simple_codenet_206780_loop_while"], "timestamp": 1754148032.148211}