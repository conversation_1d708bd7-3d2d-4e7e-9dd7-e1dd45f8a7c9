{"sample_id": "simple_codenet_174617", "dataset_name": "simple_codenet", "problem_id": "p34924", "code": "import java.util.Arrays;\npublic class ArraySort {\n    public static void main(String[] args) {\n        int[] arr = {3, 1, 4, 1, 5};\n        Arrays.sort(arr);\n    }\n}", "language": "java", "problem_description": "CodeNet problem p34924", "test_cases": "", "libraries": ["<PERSON><PERSON><PERSON>"], "difficulty": "Hard", "tags": ["codenet", "java"], "patterns": ["simple_codenet_174617_method_main", "simple_codenet_174617_class_ArraySort"], "timestamp": 1754139283.4695678}