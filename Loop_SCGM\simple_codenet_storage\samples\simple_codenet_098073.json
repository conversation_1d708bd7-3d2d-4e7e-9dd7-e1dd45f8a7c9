{"sample_id": "simple_codenet_098073", "dataset_name": "simple_codenet", "problem_id": "p19615", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p19615", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_098073_func_solve"], "timestamp": 1754139116.5387418}