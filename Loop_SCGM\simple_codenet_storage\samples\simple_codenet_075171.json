{"sample_id": "simple_codenet_075171", "dataset_name": "simple_codenet", "problem_id": "p15035", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p15035", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_075171_func_solve"], "timestamp": 1754139064.6933334}