{"sample_id": "simple_codenet_206054", "dataset_name": "massive_codenet", "problem_id": "p41211", "code": "import java.util.*;\npublic class Solution {\n    public static void main(String[] args) {\n        Scanner sc = new Scanner(System.in);\n        int n = sc.nextInt();\n        System.out.println(n * (n + 1) / 2);\n    }\n}", "language": "java", "problem_description": "CodeNet problem p41211", "test_cases": "", "libraries": ["util"], "difficulty": "Hard", "tags": ["codenet", "java"], "patterns": ["simple_codenet_206054_func_Scanner"], "timestamp": 1754148018.3232427}