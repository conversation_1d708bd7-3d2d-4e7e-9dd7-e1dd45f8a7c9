{"sample_id": "simple_codenet_074886", "dataset_name": "simple_codenet", "problem_id": "p14978", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14978", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074886_func_solve"], "timestamp": 1754139064.0285945}