{"sample_id": "simple_codenet_135141", "dataset_name": "simple_codenet", "problem_id": "p27029", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27029", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135141_func_solve"], "timestamp": 1754139201.8965347}