{"sample_id": "simple_codenet_176016", "dataset_name": "simple_codenet", "problem_id": "p35204", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35204", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_176016_func_solve"], "timestamp": 1754139285.6391718}