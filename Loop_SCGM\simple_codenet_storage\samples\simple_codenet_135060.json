{"sample_id": "simple_codenet_135060", "dataset_name": "simple_codenet", "problem_id": "p27013", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27013", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135060_func_solve"], "timestamp": 1754139201.700259}