{"sample_id": "simple_codenet_674997", "dataset_name": "simple_codenet", "problem_id": "p135000", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p135000", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_674997_func_solve"], "timestamp": 1754140222.0926244}