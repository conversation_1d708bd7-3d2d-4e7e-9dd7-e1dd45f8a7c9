{"sample_id": "simple_codenet_135177", "dataset_name": "simple_codenet", "problem_id": "p27036", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27036", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135177_func_solve"], "timestamp": 1754139201.9888828}