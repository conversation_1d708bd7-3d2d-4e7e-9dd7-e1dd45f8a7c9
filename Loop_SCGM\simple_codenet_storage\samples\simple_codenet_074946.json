{"sample_id": "simple_codenet_074946", "dataset_name": "simple_codenet", "problem_id": "p14990", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14990", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074946_func_solve"], "timestamp": 1754139064.1471922}