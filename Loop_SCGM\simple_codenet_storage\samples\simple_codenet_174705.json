{"sample_id": "simple_codenet_174705", "dataset_name": "simple_codenet", "problem_id": "p34942", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p34942", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_174705_func_solve"], "timestamp": 1754139283.614591}