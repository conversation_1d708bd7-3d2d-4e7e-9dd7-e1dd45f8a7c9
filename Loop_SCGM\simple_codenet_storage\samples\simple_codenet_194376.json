{"sample_id": "simple_codenet_194376", "dataset_name": "simple_codenet", "problem_id": "p38876", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p38876", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_194376_func_solve"], "timestamp": 1754139318.7973063}