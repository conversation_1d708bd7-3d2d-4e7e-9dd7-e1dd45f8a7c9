{"sample_id": "simple_codenet_174303", "dataset_name": "simple_codenet", "problem_id": "p34861", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p34861", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_174303_func_solve"], "timestamp": 1754139282.989548}