{"sample_id": "simple_codenet_135417", "dataset_name": "simple_codenet", "problem_id": "p27084", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27084", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135417_func_solve"], "timestamp": 1754139202.53538}