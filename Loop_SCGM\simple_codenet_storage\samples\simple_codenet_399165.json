{"sample_id": "simple_codenet_399165", "dataset_name": "simple_codenet", "problem_id": "p79834", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p79834", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_399165_func_solve"], "timestamp": 1754139677.6556945}