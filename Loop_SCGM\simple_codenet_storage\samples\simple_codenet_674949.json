{"sample_id": "simple_codenet_674949", "dataset_name": "simple_codenet", "problem_id": "p134990", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p134990", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_674949_func_solve"], "timestamp": 1754140221.9922657}