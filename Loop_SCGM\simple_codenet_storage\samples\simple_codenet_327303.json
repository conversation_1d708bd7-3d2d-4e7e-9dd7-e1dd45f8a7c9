{"sample_id": "simple_codenet_327303", "dataset_name": "simple_codenet", "problem_id": "p65461", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p65461", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_327303_func_solve"], "timestamp": 1754139536.100797}