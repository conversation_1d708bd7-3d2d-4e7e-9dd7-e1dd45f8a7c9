{"sample_id": "simple_codenet_135387", "dataset_name": "simple_codenet", "problem_id": "p27078", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p27078", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_135387_func_solve"], "timestamp": 1754139202.4705634}