{"sample_id": "simple_codenet_135484", "dataset_name": "simple_codenet", "problem_id": "p27097", "code": "#include <iostream>\nusing namespace std;\nint main() {\n    int n; cin >> n;\n    cout << n * (n + 1) / 2 << endl;\n    return 0;\n}", "language": "c++", "problem_description": "CodeNet problem p27097", "test_cases": "", "libraries": ["iostream"], "difficulty": "Medium", "tags": ["codenet", "c++"], "patterns": ["simple_codenet_135484_func_main"], "timestamp": 1754139202.726934}