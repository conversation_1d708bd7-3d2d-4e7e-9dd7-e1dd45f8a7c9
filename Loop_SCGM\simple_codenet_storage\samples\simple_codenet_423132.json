{"sample_id": "simple_codenet_423132", "dataset_name": "simple_codenet", "problem_id": "p84627", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p84627", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_423132_func_solve"], "timestamp": 1754139743.8549592}