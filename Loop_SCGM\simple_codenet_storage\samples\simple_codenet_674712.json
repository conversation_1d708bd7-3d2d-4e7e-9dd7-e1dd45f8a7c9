{"sample_id": "simple_codenet_674712", "dataset_name": "simple_codenet", "problem_id": "p134943", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p134943", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_674712_func_solve"], "timestamp": 1754140221.4956393}