{"sample_id": "simple_codenet_175938", "dataset_name": "simple_codenet", "problem_id": "p35188", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p35188", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_175938_func_solve"], "timestamp": 1754139285.4976115}