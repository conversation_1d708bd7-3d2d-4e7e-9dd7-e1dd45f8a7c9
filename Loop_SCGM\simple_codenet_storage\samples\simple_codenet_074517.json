{"sample_id": "simple_codenet_074517", "dataset_name": "simple_codenet", "problem_id": "p14904", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14904", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074517_func_solve"], "timestamp": 1754139063.3458476}