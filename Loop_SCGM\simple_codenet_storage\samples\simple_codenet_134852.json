{"sample_id": "simple_codenet_134852", "dataset_name": "simple_codenet", "problem_id": "p26971", "code": "import java.util.*;\npublic class Solution {\n    public static void main(String[] args) {\n        Scanner sc = new Scanner(System.in);\n        int n = sc.nextInt();\n        System.out.println(n * (n + 1) / 2);\n    }\n}", "language": "java", "problem_description": "CodeNet problem p26971", "test_cases": "", "libraries": ["util"], "difficulty": "Hard", "tags": ["codenet", "java"], "patterns": ["simple_codenet_134852_method_main", "simple_codenet_134852_method_Scanner", "simple_codenet_134852_class_Solution"], "timestamp": 1754139201.2141953}