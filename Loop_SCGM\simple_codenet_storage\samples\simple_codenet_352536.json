{"sample_id": "simple_codenet_352536", "dataset_name": "simple_codenet", "problem_id": "p70508", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p70508", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_352536_func_solve"], "timestamp": 1754139577.8552}