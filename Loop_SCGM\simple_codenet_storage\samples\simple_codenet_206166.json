{"sample_id": "simple_codenet_206166", "dataset_name": "massive_codenet", "problem_id": "p41234", "code": "import java.util.*;\npublic class Solution {\n    public static void main(String[] args) {\n        Scanner sc = new Scanner(System.in);\n        int n = sc.nextInt();\n        System.out.println(n * (n + 1) / 2);\n    }\n}", "language": "java", "problem_description": "CodeNet problem p41234", "test_cases": "", "libraries": ["util"], "difficulty": "Easy", "tags": ["codenet", "java"], "patterns": ["simple_codenet_206166_func_Scanner"], "timestamp": 1754148019.7695627}