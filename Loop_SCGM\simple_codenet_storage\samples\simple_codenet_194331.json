{"sample_id": "simple_codenet_194331", "dataset_name": "simple_codenet", "problem_id": "p38867", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p38867", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_194331_func_solve"], "timestamp": 1754139318.7161133}