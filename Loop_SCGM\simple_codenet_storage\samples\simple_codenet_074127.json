{"sample_id": "simple_codenet_074127", "dataset_name": "simple_codenet", "problem_id": "p14826", "code": "def solve():\n    n = int(input())\n    return sum(range(n))", "language": "python", "problem_description": "CodeNet problem p14826", "test_cases": "", "libraries": [], "difficulty": "Easy", "tags": ["codenet", "python"], "patterns": ["simple_codenet_074127_func_solve"], "timestamp": 1754139062.5115044}